// Single Source of Truth for Solving Tomorrow Business Data
// This file contains all standardized figures used across the website

const SOLVING_TOMORROW_DATA = {
    // Company Information
    company: {
        name: "Solving Tomorrow",
        founded: "2025",
        location: "Durban, South Africa",
        initialCapital: 80000,
        monthlyCosts: 300000
    },

    // Market Data
    market: {
        size: 2800000000, // R2.8 billion
        growthRate: 15, // 15% annual
        targetStudents: 12300000, // 12.3M students
        digitalAdoption: 78, // 78%
        governmentInvestment: 2500000000, // R2.5 billion (2025-2027)
        teacherShortage: 30000
    },

    // Product Pricing
    pricing: {
        lumerous: {
            individual: 200, // R200/month per user
            enterprise: 5000, // R5000/month per organization
            premium: 50 // R50-100/month add-ons (using 50 as base)
        },
        popia: {
            min: 2000, // R2000/month
            max: 5000  // R5000/month
        },
        ecommerce: {
            setup: 50000, // R50,000 one-time
            maintenance: 2000 // R2000/month
        }
    },

    // Financial Projections (Standardized)
    projections: {
        breakeven: {
            month: 5,
            users: 1500,
            monthlyRevenue: 300000 // R300K
        },
        year1: {
            users: 1800, // Average users by end of year 1
            monthlyRevenue: 360000, // R360K average
            annualRevenue: 4320000, // R4.32M (R360K × 12)
            operatingCosts: 3600000, // R3.6M
            netProfit: 720000 // R720K
        },
        year2: {
            users: 3000, // Average users in year 2
            monthlyRevenue: 600000, // R600K average
            annualRevenue: 7200000, // R7.2M
            operatingCosts: 4320000, // R4.32M
            netProfit: 2880000 // R2.88M
        },
        year3: {
            users: 6000, // Average users in year 3
            monthlyRevenue: 1200000, // R1.2M average
            annualRevenue: 14400000, // R14.4M
            operatingCosts: 7200000, // R7.2M
            netProfit: 7200000 // R7.2M
        }
    },

    // Market Segments
    segments: {
        primary: {
            name: "Individual Students & Tutors",
            size: 2000000, // 2M students
            potential: 10000000, // R10M/month potential
            targetUsers: 50000,
            pricePerUser: 200
        },
        secondary: {
            name: "Schools & Institutions",
            size: 1200, // 1,200 schools
            potential: 2500000, // R2.5M/month potential
            targetClients: 500,
            pricePerClient: 5000
        },
        tertiary: {
            name: "Tutoring Centers & NGOs",
            size: 800, // 800 centers
            potential: 600000, // R600K/month potential
            targetClients: 200,
            pricePerClient: 3000
        }
    },

    // Timeline Milestones
    milestones: {
        phase1: {
            period: "Jan-Jun 2025",
            title: "Foundation & Launch",
            targetUsers: 1500,
            targetRevenue: 300000,
            keyGoals: [
                "Complete POPIA compliance and BEE certification",
                "Finalize MVP development with core features",
                "Launch pilot program in first school",
                "Expand to 3 pilot schools, gather feedback",
                "Implement feedback, prepare for public launch",
                "Achieve breakeven: 1,500 users, R300K monthly revenue"
            ]
        },
        phase2: {
            period: "Jul-Dec 2025",
            title: "Market Penetration",
            targetUsers: 3000,
            targetRevenue: 600000,
            keyGoals: [
                "Scale to 2,000 users, launch additional services",
                "Implement profit allocation, expand to 10 organizations",
                "Reach 2,500 users, expand service portfolio",
                "Launch referral program",
                "Reach 3,000+ users, expand to 15 client partnerships"
            ]
        },
        phase3: {
            period: "2026",
            title: "Rapid Expansion",
            targetUsers: 5000,
            targetRevenue: 1000000,
            keyGoals: [
                "Launch in Western Cape and Eastern Cape",
                "Reach 3,000 users, launch premium tier",
                "50 school partnerships, enterprise features",
                "5,000+ users, market leadership established"
            ]
        },
        phase4: {
            period: "2027+",
            title: "Market Leadership & Expansion",
            targetUsers: 10000,
            targetRevenue: 2000000,
            keyGoals: [
                "Launch in Botswana and Namibia",
                "10,000+ users, advanced AI features",
                "Strategic partnerships with publishers",
                "Evaluate exit opportunities (IPO/Acquisition)"
            ]
        }
    },

    // Team Information
    team: [
        {
            id: "navenshia",
            name: "Navenshia",
            title: "Junior Full Stack Developer",
            expertise: ["React", "Node.js", "UI/UX", "JavaScript", "CSS3", "MongoDB"]
        },
        {
            id: "shelden",
            name: "Shelden",
            title: "CTO & Co-Founder",
            expertise: ["AI/ML", "Python", "Cloud Architecture", "TensorFlow", "AWS", "System Design"]
        },
        {
            id: "magenta",
            name: "Magenta",
            title: "Full Stack Developer",
            expertise: ["JavaScript", "Vue.js", "Database Design", "PostgreSQL", "Redis", "Docker"]
        },
        {
            id: "ashish",
            name: "Ashish",
            title: "AI Engineer",
            expertise: ["NLP", "TensorFlow", "Data Science", "PyTorch", "Scikit-learn", "Deep Learning"]
        },
        {
            id: "rhea",
            name: "Rhea",
            title: "QA & Testing Lead",
            expertise: ["Test Automation", "Quality Assurance", "Selenium", "Jest", "Cypress", "Performance Testing"]
        },
        {
            id: "shivek",
            name: "Shivek",
            title: "Mobile Developer",
            expertise: ["React Native", "Flutter", "Mobile UI", "iOS", "Android", "Mobile Performance"]
        },
        {
            id: "claude",
            name: "Claude",
            title: "AI Assistant & Content Specialist",
            expertise: ["Content Strategy", "AI Integration", "Educational Design", "Natural Language", "Curriculum Development", "AI Tutoring"]
        },
        {
            id: "priyanka",
            name: "Priyanka",
            title: "UX/UI Designer & Marketing",
            expertise: ["UI/UX Design", "Digital Marketing", "Brand Strategy", "Figma", "Adobe Creative Suite", "User Research"]
        }
    ],

    // Competitive Analysis
    competitors: {
        mindsetLearn: {
            name: "Mindset Learn",
            location: "Cape Town",
            marketShare: 25,
            pricing: 150, // R150/month
            strengths: ["CAPS-aligned", "school distribution network"],
            weaknesses: ["Limited personalization", "outdated UI"]
        },
        siyavula: {
            name: "Siyavula",
            location: "Cape Town",
            marketShare: 20,
            strengths: ["Strong adaptive learning for math/science"],
            weaknesses: ["Limited to STEM subjects"]
        },
        globalPlayers: {
            name: "Global Players",
            location: "International",
            marketShare: 30,
            pricing: 400, // R300-500/month average
            strengths: ["Advanced AI", "global brand recognition"],
            weaknesses: ["No CAPS alignment", "generic content"]
        }
    },

    // Operating Costs Breakdown
    costs: {
        annual: {
            cloudInfrastructure: 120000, // R120K/year
            teamSalaries: 2400000, // R2.4M/year
            marketingSales: 600000, // R600K/year
            operationsLegal: 480000, // R480K/year
            total: 3600000 // R3.6M/year
        },
        monthly: {
            total: 300000 // R300K/month
        }
    },

    // Profit Allocation (once breakeven achieved)
    profitAllocation: {
        reinvestmentGrowth: 40, // 40%
        teamOperations: 35, // 35%
        emergencyFund: 15, // 15%
        founderDistribution: 10 // 10%
    }
};

// Utility functions for formatting
const formatCurrency = (amount, showCents = false) => {
    return new Intl.NumberFormat('en-ZA', {
        style: 'currency',
        currency: 'ZAR',
        minimumFractionDigits: showCents ? 2 : 0,
        maximumFractionDigits: showCents ? 2 : 0
    }).format(amount);
};

const formatNumber = (number) => {
    return new Intl.NumberFormat('en-ZA').format(number);
};

const formatPercentage = (decimal) => {
    return (decimal * 100).toFixed(1) + '%';
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { SOLVING_TOMORROW_DATA, formatCurrency, formatNumber, formatPercentage };
}
