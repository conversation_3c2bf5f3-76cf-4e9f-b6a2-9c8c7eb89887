<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Solving Tomorrow | AI-Powered SaaS Solutions</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #FF6B35;
            --primary-dark: #E05A2B;
            --secondary: #2D2D2D;
            --light: #F7F7F7;
            --dark: #1A1A1A;
            --grey: #6C757D;
            --light-grey: #E9ECEF;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, var(--light), #ffffff);
            color: var(--dark);
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            max-width: 800px;
            padding: 2rem;
            text-align: center;
        }

        .logo {
            font-size: 3rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 1rem;
        }

        .tagline {
            font-size: 1.2rem;
            color: var(--grey);
            margin-bottom: 3rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .nav-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: 1px solid var(--light-grey);
            transition: all 0.3s ease;
            text-decoration: none;
            color: inherit;
        }

        .nav-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: var(--primary);
        }

        .nav-icon {
            font-size: 3rem;
            color: var(--primary);
            margin-bottom: 1rem;
        }

        .nav-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--secondary);
            margin-bottom: 0.5rem;
        }

        .nav-desc {
            color: var(--grey);
            font-size: 0.9rem;
        }

        .main-btn {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
        }

        .main-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
        }

        .footer {
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 1px solid var(--light-grey);
            color: var(--grey);
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .logo {
                font-size: 2.5rem;
            }

            .nav-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .nav-card {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="logo">Solving Tomorrow</h1>
        <p class="tagline">AI-Powered SaaS Solutions for South African Businesses</p>
        
        <div class="nav-grid">
            <a href="solving.html" class="nav-card">
                <div class="nav-icon">📊</div>
                <div class="nav-title">Complete Business Plan</div>
                <div class="nav-desc">Comprehensive business plan with interactive configurator and financial projections</div>
            </a>
            
            <a href="market-analysis.html" class="nav-card">
                <div class="nav-icon">📈</div>
                <div class="nav-title">Market Analysis</div>
                <div class="nav-desc">In-depth analysis of the South African EdTech market and competitive landscape</div>
            </a>
            
            <a href="product-roadmap.html" class="nav-card">
                <div class="nav-icon">🗺️</div>
                <div class="nav-title">Product Roadmap</div>
                <div class="nav-desc">Development timeline and feature roadmap for the Lumerous platform</div>
            </a>
            
            <a href="team.html" class="nav-card">
                <div class="nav-icon">👥</div>
                <div class="nav-title">Team Profiles</div>
                <div class="nav-desc">Meet the passionate innovators behind Solving Tomorrow</div>
            </a>
            
            <a href="revenue-model.html" class="nav-card">
                <div class="nav-icon">💰</div>
                <div class="nav-title">Revenue Model</div>
                <div class="nav-desc">Detailed revenue streams, financial projections, and profit allocation strategy</div>
            </a>
        </div>
        
        <a href="solving.html" class="main-btn">
            <i class="fas fa-rocket"></i> Explore Full Business Plan
        </a>
        
        <div class="footer">
            <p>© 2025 Solving Tomorrow. Revolutionizing education through AI-powered solutions.</p>
        </div>
    </div>

    <script>
        // Add smooth animations on load
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.nav-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = `all 0.6s ease-out ${index * 0.1}s`;
                
                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 100 + (index * 100));
            });

            // Add click analytics (placeholder)
            cards.forEach(card => {
                card.addEventListener('click', function() {
                    console.log('Navigation to:', this.href);
                });
            });
        });
    </script>
</body>
</html>
