<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Solving Tomorrow - Revenue Model</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
            background-color: #f5f7fa;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        h1 {
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        .revenue-stream {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #3498db;
        }
        .revenue-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .price-tag {
            background-color: #3498db;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .projection-chart {
            height: 300px;
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            margin: 30px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .calculator {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }
        input[type="number"], input[type="range"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .result-box {
            background-color: #e8f4fc;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
            border-left: 4px solid #3498db;
        }
        .profit-allocation {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .allocation-item {
            background-color: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .allocation-percent {
            font-size: 24px;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 5px;
        }
        .milestone-timeline {
            position: relative;
            padding-left: 30px;
            margin: 30px 0;
        }
        .milestone {
            position: relative;
            padding-bottom: 30px;
        }
        .milestone:last-child {
            padding-bottom: 0;
        }
        .milestone:before {
            content: "";
            position: absolute;
            left: -30px;
            top: 5px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: #3498db;
        }
        .milestone:after {
            content: "";
            position: absolute;
            left: -21px;
            top: 25px;
            width: 2px;
            height: calc(100% - 20px);
            background-color: #3498db;
        }
        .milestone:last-child:after {
            display: none;
        }
        .milestone-date {
            font-weight: bold;
            color: #3498db;
            margin-bottom: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #3498db;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <h1>Solving Tomorrow Revenue Model</h1>
    
    <div class="revenue-stream">
        <div class="revenue-header">
            <h2>Lumerous Education Platform</h2>
            <span class="price-tag">R200/month per user</span>
        </div>
        <p>Our flagship product - a subscription-based e-learning platform designed specifically for the South African curriculum (CAPS/IEB aligned).</p>
        <ul>
            <li>Primary revenue stream (60% of total revenue by Year 2)</li>
            <li>Target: 1,500 users by Month 5 (breakeven at R300,000 monthly revenue)</li>
            <li>Currently being piloted with 3 schools</li>
            <li>Features: AI-powered tutoring, curriculum alignment, progress tracking, gamified learning</li>
        </ul>
    </div>
    
    <div class="revenue-stream">
        <div class="revenue-header">
            <h2>Enterprise Licenses</h2>
            <span class="price-tag">R5,000/month per organization</span>
        </div>
        <p>Bulk solutions for schools and institutions with admin dashboards and analytics.</p>
        <ul>
            <li>Target: 30 clients by Year 1 (R150,000/month)</li>
            <li>Projection: 30% of total revenue</li>
            <li>Minimum 50 users per organization</li>
        </ul>
    </div>
    
    <div class="revenue-stream">
        <div class="revenue-header">
            <h2>Premium Features</h2>
            <span class="price-tag">R50-100/month add-ons</span>
        </div>
        <p>High-margin upsell opportunities including advanced analytics and certification.</p>
        <ul>
            <li>Projection: 15% adoption rate by Year 2</li>
            <li>Target: 10% of total revenue</li>
            <li>Examples: Advanced analytics (R50/user/month), Certification (R100/test)</li>
        </ul>
    </div>
    
    <div class="revenue-stream">
        <div class="revenue-header">
            <h2>POPIA Compliance SaaS</h2>
            <span class="price-tag">R2,000–5,000/month</span>
        </div>
        <p>AI-driven platform to automate compliance with South Africa's Protection of Personal Information Act.</p>
        <ul>
            <li>Features: Data audit and classification, automated compliance reporting, real-time monitoring</li>
            <li>Pricing based on business size</li>
            <li>Launch planned for Q3 2025</li>
        </ul>
    </div>
    
    <div class="revenue-stream">
        <div class="revenue-header">
            <h2>E-commerce Solutions</h2>
            <span class="price-tag">R50,000 one-time + R2,000/month</span>
        </div>
        <p>Custom e-commerce websites with secure payment gateways and monthly maintenance plans.</p>
        <ul>
            <li>Features: User-friendly interfaces, AI product recommendations, SEO optimization</li>
            <li>Target: 5 retail clients by Q4 2025</li>
            <li>20 e-commerce projects by Q3 2026</li>
        </ul>
    </div>
    
    <div class="projection-chart">
        <h2>3-Year Revenue Projections</h2>
        <canvas id="revenueChart"></canvas>
    </div>
    
    <div class="calculator">
        <h2>Revenue Projection Calculator</h2>
        <div class="form-group">
            <label for="newUsers">New Users Per Month:</label>
            <input type="number" id="newUsers" min="0" value="200">
        </div>
        <div class="form-group">
            <label for="premiumRate">Premium Upsell Rate (%):</label>
            <input type="range" id="premiumRate" min="0" max="30" value="10" step="1">
            <span id="premiumRateValue">10%</span>
        </div>
        <div class="form-group">
            <label for="newSchools">New Schools Per Quarter:</label>
            <input type="number" id="newSchools" min="0" value="5">
        </div>
        <div class="form-group">
            <label for="churnRate">Monthly Churn Rate (%):</label>
            <input type="range" id="churnRate" min="0" max="15" value="5" step="1">
            <span id="churnRateValue">5%</span>
        </div>
        <div class="result-box">
            <h3>Projected Monthly Revenue: <span id="projectedRevenue">R300,000</span></h3>
            <p>Breakeven Point: <span id="breakevenPoint">Month 5</span></p>
        </div>
    </div>
    
    <h2>Financial Projections</h2>
    <table>
        <thead>
            <tr>
                <th>Year</th>
                <th>Lumerous Revenue</th>
                <th>Additional Services</th>
                <th>Total Revenue</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Year 1</td>
                <td>R3.6 million</td>
                <td>R1.2 million</td>
                <td>R4.8 million</td>
            </tr>
            <tr>
                <td>Year 2</td>
                <td>R7.2 million</td>
                <td>R2.4 million</td>
                <td>R9.6 million</td>
            </tr>
            <tr>
                <td>Year 3</td>
                <td>R14.4 million</td>
                <td>R4.8 million</td>
                <td>R19.2 million</td>
            </tr>
        </tbody>
    </table>
    
    <h2>Profit Allocation Framework</h2>
    <p>Once Lumerous achieves R300,000/month (Month 5), profits will be strategically allocated:</p>
    <div class="profit-allocation">
        <div class="allocation-item">
            <div class="allocation-percent">30%</div>
            <h3>Contingency Fund</h3>
            <p>Risk mitigation and financial security (max 2 years operating costs)</p>
        </div>
        <div class="allocation-item">
            <div class="allocation-percent">20%</div>
            <h3>Research & Development</h3>
            <p>Innovation and competitive advantage</p>
        </div>
        <div class="allocation-item">
            <div class="allocation-percent">20%</div>
            <h3>Company Growth</h3>
            <p>Infrastructure and capability enhancement</p>
        </div>
        <div class="allocation-item">
            <div class="allocation-percent">15%</div>
            <h3>Team Building</h3>
            <p>Employee satisfaction and retention</p>
        </div>
        <div class="allocation-item">
            <div class="allocation-percent">15%</div>
            <h3>Passive Income</h3>
            <p>Portfolio diversification (real estate, stocks, bonds)</p>
        </div>
    </div>
    
    <h2>Key Financial Milestones</h2>
    <div class="milestone-timeline">
        <div class="milestone">
            <div class="milestone-date">Month 5</div>
            <p>Achieve breakeven with R80,000 investment: 1,500 users, R300K monthly revenue</p>
        </div>
        <div class="milestone">
            <div class="milestone-date">Month 12</div>
            <p>Scale to 3,000+ users, R600K monthly revenue, begin profit allocation</p>
        </div>
        <div class="milestone">
            <div class="milestone-date">Year 2</div>
            <p>R8.6M annual revenue, strong contingency fund, diversified income streams</p>
        </div>
        <div class="milestone">
            <div class="milestone-date">Year 3+</div>
            <p>Market leadership, passive income portfolio, potential exit opportunities</p>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="revenue_model.js"></script>
</body>
</html>