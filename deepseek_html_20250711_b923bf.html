<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Solving Tomorrow - Dream Team</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
            background-color: #f5f7fa;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        h1 {
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        .team-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }
        .team-card {
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .team-card:hover {
            transform: translateY(-5px);
        }
        .team-header {
            background-color: #3498db;
            color: white;
            padding: 15px;
            text-align: center;
        }
        .team-name {
            margin: 0;
            font-size: 20px;
        }
        .team-title {
            margin: 5px 0 0;
            font-size: 16px;
            font-weight: normal;
        }
        .team-content {
            padding: 20px;
        }
        .team-photo {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid white;
            margin: -60px auto 15px;
            display: block;
            background-color: #eee;
        }
        .team-bio {
            margin-bottom: 15px;
            font-size: 14px;
        }
        .team-expertise {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 15px;
        }
        .expertise-tag {
            background-color: #e8f4fc;
            color: #3498db;
            padding: 4px 10px;
            border-radius: 20px;
            font-size: 12px;
        }
        .team-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-top: 20px;
        }
        .stat-item {
            text-align: center;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        .stat-value {
            font-size: 20px;
            font-weight: bold;
            color: #3498db;
        }
        .stat-label {
            font-size: 12px;
            color: #7f8c8d;
        }
        .team-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
            z-index: 100;
            overflow-y: auto;
        }
        .modal-content {
            background-color: white;
            margin: 50px auto;
            max-width: 800px;
            border-radius: 8px;
            overflow: hidden;
            animation: modalFadeIn 0.3s;
        }
        @keyframes modalFadeIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .modal-header {
            background-color: #3498db;
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .modal-close {
            font-size: 24px;
            cursor: pointer;
        }
        .modal-body {
            padding: 20px;
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 20px;
        }
        .modal-photo {
            width: 100%;
            border-radius: 8px;
        }
        .modal-details h3 {
            margin-top: 0;
            color: #3498db;
        }
        .modal-projects {
            margin-top: 20px;
        }
        .project-item {
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        .project-item:last-child {
            border-bottom: none;
        }
        .project-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .project-desc {
            font-size: 14px;
            color: #7f8c8d;
        }
        @media (max-width: 768px) {
            .modal-body {
                grid-template-columns: 1fr;
            }
            .team-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <h1>The Solving Tomorrow Dream Team</h1>
    
    <div class="team-grid">
        <div class="team-card" data-member="navenshia">
            <div class="team-header">
                <h3 class="team-name">Navenshia</h3>
                <p class="team-title">Junior Full Stack Developer</p>
            </div>
            <img src="https://via.placeholder.com/120" alt="Navenshia" class="team-photo">
            <div class="team-content">
                <p class="team-bio">Developer with expertise in business strategy and educational technology. Drives company vision and stakeholder relationships.</p>
                <div class="team-expertise">
                    <span class="expertise-tag">Business Strategy</span>
                    <span class="expertise-tag">EdTech</span>
                    <span class="expertise-tag">Full Stack</span>
                    <span class="expertise-tag">Stakeholder Relations</span>
                </div>
                <div class="team-stats">
                    <div class="stat-item">
                        <div class="stat-value">5+</div>
                        <div class="stat-label">Years Experience</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">12</div>
                        <div class="stat-label">Projects</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="team-card" data-member="shelden">
            <div class="team-header">
                <h3 class="team-name">Shelden</h3>
                <p class="team-title">CTO & Co-Founder</p>
            </div>
            <img src="https://via.placeholder.com/120" alt="Shelden" class="team-photo">
            <div class="team-content">
                <p class="team-bio">AI specialist with expertise in machine learning and educational technology. Leads technical architecture and AI development.</p>
                <div class="team-expertise">
                    <span class="expertise-tag">Machine Learning</span>
                    <span class="expertise-tag">AI Architecture</span>
                    <span class="expertise-tag">EdTech</span>
                    <span class="expertise-tag">Technical Leadership</span>
                </div>
                <div class="team-stats">
                    <div class="stat-item">
                        <div class="stat-value">8+</div>
                        <div class="stat-label">Years Experience</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">7</div>
                        <div class="stat-label">AI Projects</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="team-card" data-member="magenta">
            <div class="team-header">
                <h3 class="team-name">Magenta</h3>
                <p class="team-title">Full Stack Developer</p>
            </div>
            <img src="https://via.placeholder.com/120" alt="Magenta" class="team-photo">
            <div class="team-content">
                <p class="team-bio">Infrastructure specialist with expertise in scalable cloud platforms and backend development.</p>
                <div class="team-expertise">
                    <span class="expertise-tag">Cloud Architecture</span>
                    <span class="expertise-tag">Backend</span>
                    <span class="expertise-tag">DevOps</span>
                    <span class="expertise-tag">Database</span>
                </div>
                <div class="team-stats">
                    <div class="stat-item">
                        <div class="stat-value">6+</div>
                        <div class="stat-label">Years Experience</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">15</div>
                        <div class="stat-label">Systems Built</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="team-card" data-member="ashish">
            <div class="team-header">
                <h3 class="team-name">Ashish</h3>
                <p class="team-title">AI Engineer</p>
            </div>
            <img src="https://via.placeholder.com/120" alt="Ashish" class="team-photo">
            <div class="team-content">
                <p class="team-bio">Specializes in chatbot development, natural language processing, and adaptive learning algorithms.</p>
                <div class="team-expertise">
                    <span class="expertise-tag">NLP</span>
                    <span class="expertise-tag">Chatbots</span>
                    <span class="expertise-tag">Adaptive Learning</span>
                    <span class="expertise-tag">Python</span>
                </div>
                <div class="team-stats">
                    <div class="stat-item">
                        <div class="stat-value">5+</div>
                        <div class="stat-label">Years Experience</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">9</div>
                        <div class="stat-label">AI Models</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="team-card" data-member="rhea">
            <div class="team-header">
                <h3 class="team-name">Rhea</h3>
                <p class="team-title">QA & Testing Lead</p>
            </div>
            <img src="https://via.placeholder.com/120" alt="Rhea" class="team-photo">
            <div class="team-content">
                <p class="team-bio">Ensures platform reliability, user experience optimization, and quality assurance across all devices and platforms.</p>
                <div class="team-expertise">
                    <span class="expertise-tag">Quality Assurance</span>
                    <span class="expertise-tag">UX Testing</span>
                    <span class="expertise-tag">Automation</span>
                    <span class="expertise-tag">Performance</span>
                </div>
                <div class="team-stats">
                    <div class="stat-item">
                        <div class="stat-value">4+</div>
                        <div class="stat-label">Years Experience</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">200+</div>
                        <div class="stat-label">Tests Created</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="team-card" data-member="shivek">
            <div class="team-header">
                <h3 class="team-name">Shivek</h3>
                <p class="team-title">Mobile Developer</p>
            </div>
            <img src="https://via.placeholder.com/120" alt="Shivek" class="team-photo">
            <div class="team-content">
                <p class="team-bio">Builds cross-platform mobile experiences for iOS and Android with focus on offline capabilities.</p>
                <div class="team-expertise">
                    <span class="expertise-tag">React Native</span>
                    <span class="expertise-tag">iOS</span>
                    <span class="expertise-tag">Android</span>
                    <span class="expertise-tag">Offline First</span>
                </div>
                <div class="team-stats">
                    <div class="stat-item">
                        <div class="stat-value">5+</div>
                        <div class="stat-label">Years Experience</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">8</div>
                        <div class="stat-label">Apps Built</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="team-card" data-member="claude">
            <div class="team-header">
                <h3 class="team-name">Claude</h3>
                <p class="team-title">AI Assistant & Content Specialist</p>
            </div>
            <img src="https://via.placeholder.com/120" alt="Claude" class="team-photo">
            <div class="team-content">
                <p class="team-bio">Advanced AI system providing content generation, curriculum alignment, and intelligent tutoring capabilities.</p>
                <div class="team-expertise">
                    <span class="expertise-tag">Content Generation</span>
                    <span class="expertise-tag">Curriculum Design</span>
                    <span class="expertise-tag">Tutoring Systems</span>
                    <span class="expertise-tag">LLM</span>
                </div>
                <div class="team-stats">
                    <div class="stat-item">
                        <div class="stat-value">24/7</div>
                        <div class="stat-label">Availability</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">100%</div>
                        <div class="stat-label">CAPS Aligned</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="team-card" data-member="priyanka">
            <div class="team-header">
                <h3 class="team-name">Priyanka</h3>
                <p class="team-title">UX/UI Designer & Marketing</p>
            </div>
            <img src="https://via.placeholder.com/120" alt="Priyanka" class="team-photo">
            <div class="team-content">
                <p class="team-bio">Creates intuitive user experiences and leads marketing initiatives to drive user acquisition and engagement.</p>
                <div class="team-expertise">
                    <span class="expertise-tag">UI Design</span>
                    <span class="expertise-tag">UX Research</span>
                    <span class="expertise-tag">Digital Marketing</span>
                    <span class="expertise-tag">Branding</span>
                </div>
                <div class="team-stats">
                    <div class="stat-item">
                        <div class="stat-value">5+</div>
                        <div class="stat-label">Years Experience</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">20+</div>
                        <div class="stat-label">Design Projects</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="team-modal" id="teamModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalName">Team Member</h2>
                <span class="modal-close">&times;</span>
            </div>
            <div class="modal-body">
                <img src="https://via.placeholder.com/300" alt="Team Member" class="modal-photo" id="modalPhoto">
                <div class="modal-details">
                    <h3 id="modalTitle">Position</h3>
                    <p id="modalBio">Bio information will appear here.</p>
                    <div class="team-expertise" id="modalExpertise"></div>
                    <div class="modal-projects">
                        <h4>Key Projects</h4>
                        <div class="project-item">
                            <div class="project-title">Lumerous Platform</div>
                            <div class="project-desc">Led development of core AI tutoring features</div>
                        </div>
                        <div class="project-item">
                            <div class="project-title">POPIA Compliance SaaS</div>
                            <div class="project-desc">Designed automated compliance reporting system</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="team_dashboard.js"></script>
</body>
</html>