document.addEventListener('DOMContentLoaded', function() {
    // Checkbox functionality for milestones
    const checkboxes = document.querySelectorAll('.status-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const milestoneItem = this.closest('.milestone-item');
            if (this.checked) {
                milestoneItem.style.textDecoration = 'line-through';
                milestoneItem.style.opacity = '0.7';
                milestoneItem.style.color = '#6C757D';
                
                // Add completion animation
                this.style.transform = 'scale(1.2)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 200);
            } else {
                milestoneItem.style.textDecoration = 'none';
                milestoneItem.style.opacity = '1';
                milestoneItem.style.color = '';
            }
        });
        
        // Initialize completed items
        if (checkbox.checked) {
            const milestoneItem = checkbox.closest('.milestone-item');
            milestoneItem.style.textDecoration = 'line-through';
            milestoneItem.style.opacity = '0.7';
            milestoneItem.style.color = '#6C757D';
        }
    });

    // Animate timeline phases on scroll
    const phases = document.querySelectorAll('.phase');
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateX(0)';
            }
        });
    }, { 
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });

    phases.forEach((phase, index) => {
        phase.style.opacity = '0';
        if (phase.classList.contains('left')) {
            phase.style.transform = 'translateX(-50px)';
        } else {
            phase.style.transform = 'translateX(50px)';
        }
        phase.style.transition = `all 0.6s ease-out ${index * 0.2}s`;
        observer.observe(phase);
    });

    // Animate feature cards on scroll
    const featureCards = document.querySelectorAll('.feature-card');
    const featureObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, { 
        threshold: 0.1,
        rootMargin: '0px 0px -30px 0px'
    });

    featureCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = `all 0.5s ease-out ${index * 0.1}s`;
        featureObserver.observe(card);
    });

    // Progress calculation for each phase
    function calculatePhaseProgress() {
        phases.forEach(phase => {
            const checkboxes = phase.querySelectorAll('.status-checkbox');
            const checkedBoxes = phase.querySelectorAll('.status-checkbox:checked');
            const progress = (checkedBoxes.length / checkboxes.length) * 100;
            
            // Create or update progress bar
            let progressBar = phase.querySelector('.progress-bar');
            if (!progressBar) {
                progressBar = document.createElement('div');
                progressBar.className = 'progress-bar';
                progressBar.innerHTML = `
                    <div class="progress-track">
                        <div class="progress-fill" style="width: ${progress}%"></div>
                    </div>
                    <span class="progress-text">${Math.round(progress)}% Complete</span>
                `;
                
                // Add CSS for progress bar
                if (!document.querySelector('#progress-styles')) {
                    const style = document.createElement('style');
                    style.id = 'progress-styles';
                    style.textContent = `
                        .progress-bar {
                            margin-top: 15px;
                            padding-top: 15px;
                            border-top: 1px solid var(--light-grey);
                        }
                        .progress-track {
                            width: 100%;
                            height: 8px;
                            background-color: var(--light-grey);
                            border-radius: 4px;
                            overflow: hidden;
                            margin-bottom: 8px;
                        }
                        .progress-fill {
                            height: 100%;
                            background: linear-gradient(90deg, var(--primary), var(--primary-dark));
                            border-radius: 4px;
                            transition: width 0.5s ease;
                        }
                        .progress-text {
                            font-size: 0.8rem;
                            color: var(--grey);
                            font-weight: 500;
                        }
                    `;
                    document.head.appendChild(style);
                }
                
                phase.querySelector('.phase-content').appendChild(progressBar);
            } else {
                progressBar.querySelector('.progress-fill').style.width = progress + '%';
                progressBar.querySelector('.progress-text').textContent = Math.round(progress) + '% Complete';
            }
        });
    }

    // Initial progress calculation
    calculatePhaseProgress();

    // Update progress when checkboxes change
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', calculatePhaseProgress);
    });

    // Add hover effects to metric cards
    const metricCards = document.querySelectorAll('.metric-card');
    metricCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(-3px) scale(1)';
        });
    });

    // Counter animation for metric values
    function animateCounter(element, target, duration = 2000) {
        const start = 0;
        const increment = target / (duration / 16);
        let current = start;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            
            // Format the number based on the original text
            const originalText = element.getAttribute('data-original') || element.textContent;
            let displayValue;
            
            if (originalText.includes('R') && originalText.includes('M')) {
                displayValue = 'R' + (current / 1000000).toFixed(1) + 'M';
            } else if (originalText.includes('R') && originalText.includes('K')) {
                displayValue = 'R' + (current / 1000).toFixed(0) + 'K';
            } else if (originalText.includes('%')) {
                displayValue = current.toFixed(0) + '%';
            } else if (originalText.includes('+')) {
                displayValue = current.toFixed(0) + '+';
            } else {
                displayValue = current.toFixed(0);
            }
            
            element.textContent = displayValue;
        }, 16);
    }

    // Trigger counter animations when metric cards come into view
    const metricObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const valueElement = entry.target.querySelector('.metric-value');
                const originalText = valueElement.textContent;
                valueElement.setAttribute('data-original', originalText);
                
                // Extract numeric value
                let numericValue;
                if (originalText.includes('R') && originalText.includes('M')) {
                    numericValue = parseFloat(originalText.replace(/[^\d.]/g, '')) * 1000000;
                } else if (originalText.includes('R') && originalText.includes('K')) {
                    numericValue = parseFloat(originalText.replace(/[^\d.]/g, '')) * 1000;
                } else if (originalText.includes('%')) {
                    numericValue = parseFloat(originalText.replace('%', ''));
                } else if (originalText.includes('+')) {
                    numericValue = parseFloat(originalText.replace(/[^\d.]/g, ''));
                } else {
                    numericValue = parseFloat(originalText.replace(/[^\d.]/g, ''));
                }
                
                // Start animation after a delay
                setTimeout(() => {
                    animateCounter(valueElement, numericValue, 1500);
                }, 300);
                
                metricObserver.unobserve(entry.target);
            }
        });
    }, { threshold: 0.5 });

    metricCards.forEach(card => {
        metricObserver.observe(card);
    });

    // Add click-to-expand functionality for feature cards
    featureCards.forEach(card => {
        card.addEventListener('click', function() {
            // Toggle expanded state
            this.classList.toggle('expanded');
            
            // Add expanded styles if not already present
            if (!document.querySelector('#expanded-styles')) {
                const style = document.createElement('style');
                style.id = 'expanded-styles';
                style.textContent = `
                    .feature-card.expanded {
                        transform: scale(1.05);
                        z-index: 10;
                        position: relative;
                    }
                    .feature-card.expanded::after {
                        content: 'Click to collapse';
                        position: absolute;
                        bottom: 10px;
                        right: 15px;
                        font-size: 0.7rem;
                        color: var(--grey);
                        opacity: 0.7;
                    }
                `;
                document.head.appendChild(style);
            }
        });
    });

    // Smooth scrolling for any internal links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Add keyboard navigation for checkboxes
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.checked = !this.checked;
                this.dispatchEvent(new Event('change'));
            }
        });
    });
});
