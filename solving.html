<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Solving Tomorrow | AI-Powered SaaS Solutions</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary: #FF6B35;
            --primary-dark: #E05A2B;
            --secondary: #2D2D2D;
            --light: #F7F7F7;
            --dark: #1A1A1A;
            --grey: #6C757D;
            --light-grey: #E9ECEF;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background-color: var(--light);
            color: var(--dark);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* Navigation */
        header {
            background-color: var(--dark);
            color: var(--light);
            padding: 1rem 2rem;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            height: 80px;
        }

        header.scrolled {
            padding: 0.5rem 2rem;
            background-color: rgba(26, 26, 26, 0.95);
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            height: 100%;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--primary);
            text-decoration: none;
        }

        .logo span {
            color: var(--light);
        }

        /* Left Sidebar Navigation */
        .sidebar {
            position: fixed;
            top: 80px;
            left: 0;
            width: 300px;
            height: calc(100vh - 80px);
            background-color: var(--light);
            border-right: 2px solid var(--light-grey);
            overflow-y: auto;
            z-index: 999;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .sidebar.hidden {
            transform: translateX(-100%);
        }

        .sidebar-content {
            padding: 2rem 0;
        }

        .sidebar h3 {
            padding: 0 1.5rem 1rem;
            color: var(--secondary);
            font-size: 1.1rem;
            border-bottom: 2px solid var(--primary);
            margin-bottom: 1rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .nav-tabs {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .nav-tab {
            margin-bottom: 0.5rem;
        }

        .nav-tab a {
            display: block;
            padding: 1rem 1.5rem;
            color: var(--grey);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
            position: relative;
        }

        .nav-tab a:hover {
            background-color: rgba(255, 107, 53, 0.1);
            color: var(--primary);
            border-left-color: var(--primary);
        }

        .nav-tab a.active {
            background-color: rgba(255, 107, 53, 0.15);
            color: var(--primary);
            border-left-color: var(--primary);
            font-weight: 600;
        }

        /* Main Content Area */
        .main-content {
            margin-left: 300px;
            margin-top: 80px;
            min-height: calc(100vh - 80px);
            transition: margin-left 0.3s ease;
        }

        .main-content.expanded {
            margin-left: 0;
        }

        /* Mobile Menu Toggle */
        .sidebar-toggle {
            display: none;
            background: none;
            border: none;
            color: var(--light);
            font-size: 1.5rem;
            cursor: pointer;
            margin-left: auto;
        }

        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            color: var(--light);
            font-size: 1.5rem;
            cursor: pointer;
        }

        /* Hero Section */
        .hero {
            height: 100vh;
            background: linear-gradient(rgba(45, 45, 45, 0.8), rgba(45, 45, 45, 0.8)), url('https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1471&q=80') no-repeat center center/cover;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: var(--light);
            padding: 0 2rem;
        }

        .hero-content {
            max-width: 800px;
            animation: fadeIn 1.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Appendix Styles */
        .appendix-section {
            background-color: white;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid var(--primary);
        }

        .appendix-section h3 {
            color: var(--primary);
            border-bottom: 2px solid var(--light-grey);
            padding-bottom: 1rem;
            margin-bottom: 2rem;
        }

        .appendix-section h4 {
            color: var(--secondary);
            margin-top: 2rem;
            margin-bottom: 1rem;
        }

        .brand-positioning {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .positioning-item {
            background-color: var(--light);
            padding: 1.5rem;
            border-radius: 8px;
            border-left: 3px solid var(--primary);
        }

        .positioning-item h5 {
            color: var(--primary);
            margin-bottom: 1rem;
        }

        .awareness-phase {
            margin-bottom: 2rem;
            padding: 1.5rem;
            background-color: #f8f9fa;
            border-radius: 8px;
        }

        .awareness-phase h5 {
            color: var(--secondary);
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }

        .strategy-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .strategy-item {
            background-color: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .strategy-item h6 {
            color: var(--primary);
            margin-bottom: 1rem;
        }

        .strategy-item ul {
            margin-bottom: 1rem;
        }

        .strategy-item p {
            font-weight: 600;
            color: var(--secondary);
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-top: 1rem;
        }

        .metric-item, .metric-category {
            background-color: var(--light);
            padding: 1.5rem;
            border-radius: 8px;
            border-left: 3px solid var(--primary);
        }

        .metric-item h5, .metric-category h5 {
            color: var(--primary);
            margin-bottom: 1rem;
        }

        .client-segments {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .segment-card {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            padding: 1.5rem;
            border-radius: 8px;
            text-align: center;
        }

        .segment-card h5 {
            color: white;
            margin-bottom: 0.5rem;
        }

        .approach-phase {
            margin-bottom: 2rem;
            padding: 1.5rem;
            background-color: white;
            border-radius: 8px;
            border: 1px solid var(--light-grey);
        }

        .approach-phase h5 {
            color: var(--primary);
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        .phase-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 1rem;
        }

        .steps-column, .deliverables-column {
            background-color: var(--light);
            padding: 1rem;
            border-radius: 6px;
        }

        .steps-column h6, .deliverables-column h6 {
            color: var(--secondary);
            margin-bottom: 0.5rem;
        }

        .service-approach {
            margin-bottom: 3rem;
            padding: 2rem;
            background-color: #f8f9fa;
            border-radius: 12px;
            border-left: 4px solid var(--blue);
        }

        .service-approach h4 {
            color: var(--blue);
            margin-bottom: 1rem;
        }

        .service-overview {
            background-color: white;
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 1.5rem;
        }

        .timeline-phase {
            margin-bottom: 1.5rem;
            padding: 1.5rem;
            background-color: white;
            border-radius: 8px;
            border-left: 3px solid var(--green);
        }

        .timeline-phase h5 {
            color: var(--green);
            margin-bottom: 1rem;
        }

        .phase-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
        }

        .steps, .deliverables {
            background-color: var(--light);
            padding: 1rem;
            border-radius: 6px;
        }

        .steps strong, .deliverables strong {
            color: var(--secondary);
            display: block;
            margin-bottom: 0.5rem;
        }

        .success-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .success-table th {
            background-color: var(--primary);
            color: white;
            padding: 1rem;
            text-align: left;
            font-weight: 600;
        }

        .success-table td {
            padding: 1rem;
            border-bottom: 1px solid var(--light-grey);
        }

        .success-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .success-table tr:hover {
            background-color: #e9ecef;
        }

        /* Responsive Design for Appendix */
        @media (max-width: 768px) {
            .appendix-section {
                padding: 1rem;
            }

            .brand-positioning,
            .strategy-grid,
            .metrics-grid,
            .client-segments {
                grid-template-columns: 1fr;
            }

            .phase-content,
            .phase-details {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .success-table {
                font-size: 0.9rem;
            }

            .success-table th,
            .success-table td {
                padding: 0.5rem;
            }
        }



        .hero h1 {
            font-size: 3.5rem;
            margin-bottom: 1rem;
            line-height: 1.2;
        }

        .hero h1 span {
            color: var(--primary);
        }

        .hero p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .btn {
            display: inline-block;
            background-color: var(--primary);
            color: var(--light);
            padding: 0.8rem 2rem;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: 2px solid var(--primary);
        }

        .btn:hover {
            background-color: transparent;
            color: var(--primary);
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(255, 107, 53, 0.2);
        }

        .btn-outline {
            background-color: transparent;
            color: var(--primary);
            margin-left: 1rem;
        }

        .btn-outline:hover {
            background-color: var(--primary);
            color: var(--light);
        }

        /* About Section */
        .section {
            padding: 6rem 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .section-title {
            text-align: center;
            margin-bottom: 3rem;
            position: relative;
        }

        .section-title h2 {
            font-size: 2.5rem;
            color: var(--secondary);
            margin-bottom: 1rem;
        }

        .section-title h2 span {
            color: var(--primary);
        }

        .section-title::after {
            content: '';
            position: absolute;
            width: 80px;
            height: 4px;
            background-color: var(--primary);
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
        }

        .about-content {
            display: flex;
            align-items: center;
            gap: 3rem;
        }

        .about-text {
            flex: 1;
        }

        .about-text h3 {
            font-size: 1.8rem;
            margin-bottom: 1.5rem;
            color: var(--secondary);
        }

        .about-text p {
            margin-bottom: 1.5rem;
            color: var(--grey);
        }

        .about-image {
            flex: 1;
            position: relative;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.5s ease;
        }

        .about-image:hover {
            transform: scale(1.03);
        }

        .about-image img {
            width: 100%;
            height: auto;
            display: block;
        }

        /* Executive Summary Section */
        .executive-summary {
            background-color: var(--light);
        }

        .executive-content {
            display: grid;
            gap: 3rem;
        }

        .executive-overview h3 {
            font-size: 2rem;
            margin-bottom: 1.5rem;
            color: var(--secondary);
            text-align: center;
        }

        .executive-overview p {
            font-size: 1.1rem;
            margin-bottom: 2rem;
            color: var(--grey);
            text-align: center;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        .key-highlights {
            margin-top: 2rem;
        }

        .highlight-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
        }

        .highlight-card {
            background-color: var(--light);
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            border-left: 4px solid var(--primary);
            transition: transform 0.3s ease;
        }

        .highlight-card:hover {
            transform: translateY(-5px);
        }

        .highlight-card h4 {
            font-size: 1.2rem;
            margin-bottom: 1rem;
            color: var(--secondary);
        }

        .highlight-card ul {
            list-style: none;
        }

        .highlight-card li {
            margin-bottom: 0.8rem;
            position: relative;
            padding-left: 1.5rem;
            color: var(--grey);
        }

        .highlight-card li::before {
            content: '\f00c';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            left: 0;
            color: var(--primary);
        }

        .value-proposition h3 {
            font-size: 2rem;
            margin-bottom: 2rem;
            color: var(--secondary);
            text-align: center;
        }

        .value-points {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .value-point {
            background-color: var(--light);
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .value-point:hover {
            transform: translateY(-5px);
        }

        .value-point i {
            font-size: 3rem;
            color: var(--primary);
            margin-bottom: 1rem;
        }

        .value-point h4 {
            font-size: 1.3rem;
            margin-bottom: 1rem;
            color: var(--secondary);
        }

        .value-point p {
            color: var(--grey);
            line-height: 1.6;
        }

        .business-model-summary h3 {
            font-size: 2rem;
            margin-bottom: 2rem;
            color: var(--secondary);
            text-align: center;
        }

        .model-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 3rem;
        }

        .revenue-streams h4,
        .cost-structure h4 {
            font-size: 1.3rem;
            margin-bottom: 1.5rem;
            color: var(--secondary);
            text-align: center;
        }

        .stream-item,
        .cost-item {
            display: grid;
            grid-template-columns: 1fr auto auto;
            gap: 1rem;
            padding: 1rem;
            background-color: var(--light);
            border-radius: 5px;
            margin-bottom: 1rem;
            align-items: center;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        .stream-name,
        .cost-category {
            font-weight: 600;
            color: var(--secondary);
        }

        .stream-price,
        .cost-amount {
            font-weight: 700;
            color: var(--primary);
        }

        .stream-target {
            font-size: 0.9rem;
            color: var(--grey);
            font-style: italic;
        }

        .investment-summary h3 {
            font-size: 2rem;
            margin-bottom: 2rem;
            color: var(--secondary);
            text-align: center;
        }

        .investment-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 3rem;
        }

        .investment-ask,
        .investor-returns {
            background-color: var(--light);
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .investment-ask {
            border-left: 4px solid var(--primary);
        }

        .investor-returns {
            border-left: 4px solid #28a745;
        }

        .investment-ask h4,
        .investor-returns h4 {
            font-size: 1.3rem;
            margin-bottom: 1rem;
            color: var(--secondary);
        }

        .investment-ask p {
            margin-bottom: 1.5rem;
            color: var(--grey);
        }

        .use-of-funds h5 {
            font-size: 1.1rem;
            margin-bottom: 1rem;
            color: var(--primary);
        }

        .funds-allocation {
            display: grid;
            gap: 1rem;
        }

        .fund-item {
            display: grid;
            grid-template-columns: auto 1fr auto;
            gap: 1rem;
            padding: 1rem;
            background-color: rgba(255, 107, 53, 0.05);
            border-radius: 5px;
            align-items: center;
        }

        .fund-percentage {
            font-weight: 700;
            color: var(--primary);
            font-size: 1.1rem;
        }

        .fund-purpose {
            color: var(--secondary);
            font-weight: 500;
        }

        .fund-amount {
            font-weight: 700;
            color: var(--primary);
        }

        .returns-timeline {
            display: grid;
            gap: 1.5rem;
        }

        .return-milestone {
            padding: 1rem;
            background-color: rgba(40, 167, 69, 0.05);
            border-radius: 5px;
            border-left: 3px solid #28a745;
        }

        .return-milestone h5 {
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
            color: #28a745;
        }

        .return-milestone p {
            color: var(--grey);
            margin: 0;
        }

        /* Bootstrap Strategy Section */
        .bootstrap-strategy h3 {
            font-size: 2rem;
            margin-bottom: 2rem;
            color: var(--secondary);
            text-align: center;
        }

        .bootstrap-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 3rem;
        }

        .bootstrap-approach,
        .growth-milestones {
            background-color: var(--light);
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .bootstrap-approach {
            border-left: 4px solid var(--primary);
        }

        .growth-milestones {
            border-left: 4px solid #28a745;
        }

        .bootstrap-approach h4,
        .growth-milestones h4 {
            font-size: 1.3rem;
            margin-bottom: 1rem;
            color: var(--secondary);
        }

        .bootstrap-approach p {
            margin-bottom: 1.5rem;
            color: var(--grey);
        }

        .bootstrap-advantages h5 {
            font-size: 1.1rem;
            margin-bottom: 1rem;
            color: var(--primary);
        }

        .advantage-items {
            display: grid;
            gap: 1rem;
        }

        .advantage-item {
            display: grid;
            grid-template-columns: auto auto 1fr;
            gap: 1rem;
            padding: 1rem;
            background-color: rgba(255, 107, 53, 0.05);
            border-radius: 8px;
            align-items: center;
        }

        .advantage-icon {
            font-size: 1.5rem;
        }

        .advantage-title {
            font-weight: 600;
            color: var(--secondary);
        }

        .advantage-desc {
            color: var(--grey);
            font-size: 0.9rem;
        }

        .milestone-timeline {
            display: grid;
            gap: 1.5rem;
        }

        .milestone-item {
            padding: 1.5rem;
            background-color: rgba(40, 167, 69, 0.05);
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }

        .milestone-item h5 {
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
            color: #28a745;
        }

        .milestone-item p {
            color: var(--grey);
            margin: 0;
        }

        /* Business Configurator Styles */
        .configurator {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-top: 4px solid var(--primary);
        }

        .config-intro {
            text-align: center;
            max-width: 800px;
            margin: 0 auto 3rem;
            padding: 2rem;
            background-color: var(--light);
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .config-intro h3 {
            color: var(--secondary);
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }

        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .config-card {
            background-color: var(--light);
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            border-left: 4px solid var(--primary);
        }

        .config-card h4 {
            color: var(--secondary);
            margin-bottom: 1.5rem;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .config-group {
            margin-bottom: 1.5rem;
        }

        .config-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--secondary);
        }

        .config-group input,
        .config-group select {
            width: 100%;
            padding: 0.8rem;
            border: 2px solid var(--light-grey);
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .config-group input:focus,
        .config-group select:focus {
            outline: none;
            border-color: var(--primary);
        }

        .config-actions {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .config-results {
            background-color: var(--light);
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            border-left: 4px solid #28a745;
        }

        .config-results h4 {
            color: var(--secondary);
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background-color: rgba(40, 167, 69, 0.1);
            border-radius: 8px;
        }

        .result-label {
            font-weight: 600;
            color: var(--secondary);
        }

        .result-value {
            font-weight: 700;
            color: #28a745;
            font-size: 1.1rem;
        }

        /* Dynamic content highlighting */
        .dynamic-content {
            background-color: rgba(255, 107, 53, 0.15);
            padding: 0.2rem 0.4rem;
            border-radius: 4px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 107, 53, 0.2);
            font-weight: 600;
        }

        .dynamic-content.updated {
            background-color: rgba(40, 167, 69, 0.3);
            border-color: #28a745;
            animation: highlight 2s ease-in-out;
            transform: scale(1.05);
        }

        @keyframes highlight {
            0% {
                background-color: rgba(255, 107, 53, 0.5);
                transform: scale(1.1);
                box-shadow: 0 0 10px rgba(255, 107, 53, 0.5);
            }
            50% {
                background-color: rgba(40, 167, 69, 0.4);
                transform: scale(1.08);
                box-shadow: 0 0 15px rgba(40, 167, 69, 0.4);
            }
            100% {
                background-color: rgba(40, 167, 69, 0.3);
                transform: scale(1.05);
                box-shadow: 0 0 5px rgba(40, 167, 69, 0.3);
            }
        }

        /* Pulse animation for better visibility */
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .dynamic-content:hover {
            background-color: rgba(255, 107, 53, 0.25);
            cursor: help;
        }

        /* PDF Export Styles */
        @media print {
            /* Hide navigation and configurator for PDF */
            .sidebar,
            .configurator,
            header,
            .config-actions,
            .sidebar-toggle {
                display: none !important;
            }

            /* Adjust main content for PDF */
            .main-content {
                margin-left: 0 !important;
                margin-top: 0 !important;
                width: 100% !important;
                max-width: none !important;
            }

            /* Optimize sections for PDF */
            .section {
                page-break-inside: avoid;
                margin-bottom: 2rem;
                padding: 1rem 0;
            }

            .section-title {
                page-break-after: avoid;
                margin-bottom: 1rem;
            }

            /* Ensure proper page breaks */
            .hero {
                page-break-after: always;
                height: auto;
                min-height: 50vh;
                padding: 3rem 1rem;
            }

            /* Remove dynamic content highlighting for clean PDF */
            .dynamic-content {
                background-color: transparent !important;
                border: none !important;
                padding: 0 !important;
                font-weight: normal !important;
            }

            /* Optimize colors for print */
            body {
                background: white !important;
                color: black !important;
                font-size: 12pt;
                line-height: 1.4;
            }

            .section {
                background: white !important;
                color: black !important;
            }

            /* Ensure headings are visible */
            h1, h2, h3, h4, h5, h6 {
                color: #333 !important;
                page-break-after: avoid;
            }

            /* Optimize tables and grids for print */
            .highlight-grid,
            .swot-grid,
            .config-grid,
            .results-grid {
                display: block !important;
            }

            .highlight-card,
            .swot-card,
            .config-card {
                display: block !important;
                margin-bottom: 1rem;
                page-break-inside: avoid;
            }

            /* Ensure buttons and links are readable */
            .btn {
                background: #f0f0f0 !important;
                color: black !important;
                border: 1px solid #ccc !important;
            }

            /* Footer adjustments */
            footer {
                page-break-before: always;
            }

            /* Remove shadows and effects for clean print */
            * {
                box-shadow: none !important;
                text-shadow: none !important;
            }

            /* Ensure images fit properly */
            img {
                max-width: 100% !important;
                height: auto !important;
            }
        }

        /* PDF Export Button Styling */
        #exportPDF {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            border: none;
            font-weight: 600;
        }

        #exportPDF:hover {
            background: linear-gradient(135deg, #c82333, #a71e2a);
            transform: translateY(-2px);
        }

        /* PDF Export Body Class */
        .pdf-export .sidebar,
        .pdf-export .configurator {
            display: none !important;
        }

        .pdf-export .main-content {
            margin-left: 0 !important;
            margin-top: 0 !important;
        }

        /* Additional print optimizations */
        @media print {
            /* Ensure proper page margins */
            @page {
                margin: 1in;
                size: A4;
            }

            /* Optimize font sizes for print */
            body {
                font-size: 11pt !important;
                line-height: 1.3 !important;
            }

            h1 { font-size: 18pt !important; }
            h2 { font-size: 16pt !important; }
            h3 { font-size: 14pt !important; }
            h4 { font-size: 12pt !important; }

            /* Ensure proper spacing */
            .section {
                margin-bottom: 1.5rem !important;
                padding: 0.5rem 0 !important;
            }

            /* Optimize hero section for first page */
            .hero {
                background: white !important;
                color: black !important;
                text-align: center;
                padding: 2rem 1rem !important;
                border-bottom: 2px solid #333;
            }

            .hero h1 {
                color: #333 !important;
                margin-bottom: 1rem;
            }

            .hero p {
                color: #666 !important;
                font-size: 12pt !important;
            }

            /* Hide buttons in print */
            .btn,
            .btn-container {
                display: none !important;
            }

            /* Optimize tables and lists */
            ul, ol {
                margin: 0.5rem 0;
                padding-left: 1.5rem;
            }

            li {
                margin-bottom: 0.3rem;
            }

            /* Ensure proper contrast */
            .section-title h2 {
                color: #333 !important;
                border-bottom: 1px solid #ccc;
                padding-bottom: 0.5rem;
            }
        }

        /* Pulse animation for loading */
        @keyframes pulse {
            0% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
            50% { opacity: 0.8; transform: translate(-50%, -50%) scale(1.05); }
            100% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
        }

        /* Implementation Timeline Section */
        .implementation-timeline {
            background-color: var(--light-grey);
        }

        .timeline-content {
            display: grid;
            gap: 3rem;
        }

        .timeline-overview {
            text-align: center;
            max-width: 800px;
            margin: 0 auto;
        }

        .timeline-overview h3 {
            font-size: 2rem;
            margin-bottom: 1.5rem;
            color: var(--secondary);
        }

        .timeline-overview p {
            color: var(--grey);
            font-size: 1.1rem;
        }

        .timeline-phases {
            display: grid;
            gap: 3rem;
        }

        .phase-card {
            background-color: var(--light);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .phase-card:hover {
            transform: translateY(-5px);
        }

        .phase-header {
            padding: 2rem;
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .phase-1 .phase-header {
            background: linear-gradient(135deg, #FF6B35, #E05A2B);
        }

        .phase-2 .phase-header {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .phase-3 .phase-header {
            background: linear-gradient(135deg, #007bff, #6610f2);
        }

        .phase-4 .phase-header {
            background: linear-gradient(135deg, #6f42c1, #e83e8c);
        }

        .phase-header h4 {
            font-size: 1.5rem;
            margin: 0;
        }

        .phase-duration {
            font-size: 0.9rem;
            opacity: 0.9;
            background-color: rgba(255, 255, 255, 0.2);
            padding: 0.5rem 1rem;
            border-radius: 20px;
        }

        .phase-content {
            padding: 2rem;
            display: grid;
            gap: 2rem;
        }

        .phase-objectives h5,
        .phase-milestones h5,
        .phase-metrics h5 {
            font-size: 1.2rem;
            margin-bottom: 1rem;
            color: var(--secondary);
            border-bottom: 2px solid var(--primary);
            padding-bottom: 0.5rem;
        }

        .phase-objectives ul {
            list-style: none;
        }

        .phase-objectives li {
            margin-bottom: 0.8rem;
            position: relative;
            padding-left: 1.5rem;
            color: var(--grey);
        }

        .phase-objectives li::before {
            content: '\f00c';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            left: 0;
            color: var(--primary);
        }

        .milestone-timeline {
            display: grid;
            gap: 1rem;
        }

        .milestone-item {
            display: grid;
            grid-template-columns: auto 1fr;
            gap: 1rem;
            padding: 1rem;
            background-color: rgba(255, 107, 53, 0.05);
            border-radius: 8px;
            border-left: 4px solid var(--primary);
            align-items: center;
        }

        .milestone-month {
            font-weight: 600;
            color: var(--primary);
            font-size: 0.9rem;
            min-width: 80px;
        }

        .milestone-task {
            color: var(--grey);
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
        }

        .metric-item {
            text-align: center;
            padding: 1.5rem 1rem;
            background-color: rgba(255, 107, 53, 0.05);
            border-radius: 10px;
            border: 2px solid rgba(255, 107, 53, 0.1);
        }

        .metric-value {
            display: block;
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 0.5rem;
        }

        .metric-label {
            font-size: 0.9rem;
            color: var(--grey);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Services Section */
        .services {
            background-color: var(--light-grey);
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .service-card {
            background-color: var(--light);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .service-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }

        .service-icon {
            background-color: var(--primary);
            color: var(--light);
            font-size: 2.5rem;
            padding: 2rem;
            text-align: center;
        }

        .service-content {
            padding: 1.5rem;
        }

        .service-content h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: var(--secondary);
        }

        .service-content p {
            color: var(--grey);
            margin-bottom: 1.5rem;
        }

        .service-features {
            margin-top: 1rem;
        }

        .service-features li {
            margin-bottom: 0.5rem;
            color: var(--grey);
            position: relative;
            padding-left: 1.5rem;
        }

        .service-features li::before {
            content: '\f00c';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            left: 0;
            color: var(--primary);
        }

        /* Lumerous Section */
        .lumerous {
            background-color: var(--dark);
            color: var(--light);
        }

        .lumerous .section-title h2 {
            color: var(--light);
        }

        .lumerous-content {
            display: flex;
            align-items: center;
            gap: 3rem;
        }

        .lumerous-text {
            flex: 1;
        }

        .lumerous-text h3 {
            font-size: 1.8rem;
            margin-bottom: 1.5rem;
            color: var(--light);
        }

        .lumerous-text p {
            margin-bottom: 1.5rem;
            opacity: 0.9;
        }

        .lumerous-features {
            margin-top: 2rem;
        }

        .lumerous-features li {
            margin-bottom: 1rem;
            position: relative;
            padding-left: 2rem;
            opacity: 0.9;
        }

        .lumerous-features li::before {
            content: '\f058';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            left: 0;
            color: var(--primary);
        }

        .lumerous-image {
            flex: 1;
            position: relative;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }

        .lumerous-image img {
            width: 100%;
            height: auto;
            display: block;
        }

        /* Team Section */
        .team {
            background-color: var(--light-grey);
        }

        .team-members {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .team-member {
            background-color: var(--light);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            text-align: center;
        }

        .team-member:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }

        .member-image {
            width: 100%;
            height: 300px;
            overflow: hidden;
        }

        .member-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s ease;
        }

        .team-member:hover .member-image img {
            transform: scale(1.1);
        }

        .member-info {
            padding: 1.5rem;
        }

        .member-info h3 {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            color: var(--secondary);
        }

        .member-info .role {
            color: var(--primary);
            font-weight: 600;
            margin-bottom: 1rem;
            display: block;
        }

        .member-info p {
            color: var(--grey);
            margin-bottom: 1.5rem;
        }

        .social-links {
            display: flex;
            justify-content: center;
            gap: 1rem;
        }

        .social-links a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background-color: var(--light-grey);
            color: var(--secondary);
            transition: all 0.3s ease;
        }

        .social-links a:hover {
            background-color: var(--primary);
            color: var(--light);
        }

        /* Revenue Model Section */
        .revenue-model {
            background-color: var(--dark);
            color: var(--light);
        }

        .revenue-model .section-title h2 {
            color: var(--light);
        }

        .revenue-content {
            display: flex;
            flex-direction: column;
            gap: 3rem;
        }

        .revenue-intro {
            text-align: center;
            max-width: 800px;
            margin: 0 auto;
        }

        .revenue-intro p {
            opacity: 0.9;
        }

        .breakeven-explanation {
            margin-top: 2rem;
            padding: 2rem;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .breakeven-explanation h4 {
            font-size: 1.3rem;
            margin-bottom: 1.5rem;
            color: var(--primary);
            text-align: center;
        }

        .calculation-breakdown {
            display: grid;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .calc-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 5px;
        }

        .calc-item.total {
            background-color: rgba(255, 107, 53, 0.2);
            border-left: 4px solid var(--primary);
            font-weight: 600;
        }

        .calc-label {
            color: var(--light);
            opacity: 0.9;
        }

        .calc-value {
            color: var(--primary);
            font-weight: 600;
        }

        .breakeven-explanation p {
            margin: 0;
            font-style: italic;
            opacity: 0.8;
            text-align: center;
        }

        .revenue-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .revenue-card {
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 2rem;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .revenue-card:hover {
            transform: translateY(-5px);
            background-color: rgba(255, 255, 255, 0.1);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .revenue-card h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: var(--primary);
        }

        .revenue-card p {
            margin-bottom: 1.5rem;
            opacity: 0.8;
        }

        .chart-container {
            margin-top: 3rem;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .chart-controls {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .chart-controls button {
            background-color: var(--primary);
            color: var(--light);
            border: none;
            padding: 0.5rem 1.5rem;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .chart-controls button:hover {
            background-color: var(--primary-dark);
        }

        .chart-controls button.active {
            background-color: var(--light);
            color: var(--primary);
        }

        .revenue-calculator {
            margin-top: 3rem;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .calculator-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 0.8rem;
            border-radius: 5px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            background-color: rgba(255, 255, 255, 0.05);
            color: var(--light);
            font-family: 'Poppins', sans-serif;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: var(--primary);
        }

        .calculator-results {
            margin-top: 2rem;
            padding: 1.5rem;
            background-color: rgba(255, 107, 53, 0.1);
            border-radius: 5px;
            border-left: 4px solid var(--primary);
        }

        .calculator-results h4 {
            margin-bottom: 1rem;
            color: var(--primary);
        }

        .result-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px dashed rgba(255, 255, 255, 0.1);
        }

        .result-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .result-item .label {
            font-weight: 500;
        }

        .result-item .value {
            font-weight: 600;
            color: var(--primary);
        }
		        /* Competitor Analysis Section */
        .competitor-analysis {
            background-color: var(--light-grey);
        }

        .competitor-overview {
            margin-bottom: 3rem;
            text-align: center;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        .competitor-overview h3 {
            font-size: 1.8rem;
            margin-bottom: 1rem;
            color: var(--secondary);
        }

        .key-competitors {
            background-color: var(--light);
            padding: 1.5rem;
            border-radius: 10px;
            margin-top: 2rem;
            border-left: 4px solid var(--primary);
        }

        .key-competitors h4 {
            color: var(--secondary);
            margin-bottom: 1rem;
        }

        .key-competitors ul {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            list-style: none;
            padding: 0;
        }

        .key-competitors li {
            background-color: rgba(255, 107, 53, 0.1);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            border: 1px solid rgba(255, 107, 53, 0.2);
            font-weight: 500;
        }

        .competitors-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .competitor-card {
            background-color: var(--light);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .competitor-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }

        .competitor-header {
            background-color: var(--secondary);
            color: var(--light);
            padding: 1.5rem;
        }

        .competitor-header h4 {
            font-size: 1.3rem;
            margin-bottom: 0.5rem;
            color: var(--primary);
        }

        .competitor-location {
            display: block;
            font-size: 0.9rem;
            opacity: 0.8;
            margin-bottom: 0.5rem;
        }

        .competitor-status {
            display: inline-block;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .competitor-status.increasing {
            background-color: rgba(40, 167, 69, 0.2);
            color: #28a745;
        }

        .competitor-status.steady {
            background-color: rgba(255, 193, 7, 0.2);
            color: #ffc107;
        }

        .competitor-status.decreasing {
            background-color: rgba(220, 53, 69, 0.2);
            color: #dc3545;
        }

        .competitor-content {
            padding: 1.5rem;
        }

        .competitor-section {
            margin-bottom: 1.5rem;
        }

        .competitor-section h5 {
            font-size: 1rem;
            margin-bottom: 0.8rem;
            color: var(--primary);
            border-bottom: 1px solid var(--light-grey);
            padding-bottom: 0.3rem;
        }

        .competitor-section ul {
            list-style: none;
            margin-left: 0;
        }

        .competitor-section li {
            margin-bottom: 0.5rem;
            position: relative;
            padding-left: 1.2rem;
            font-size: 0.9rem;
        }

        .competitor-section li::before {
            content: '\f054';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            left: 0;
            color: var(--grey);
            font-size: 0.7rem;
        }

        .competitive-advantage {
            margin: 3rem 0;
        }

        .competitive-advantage h3 {
            font-size: 2rem;
            margin-bottom: 2rem;
            text-align: center;
            color: var(--secondary);
        }

        .advantage-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
        }

        .advantage-card {
            background-color: var(--light);
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            border-left: 4px solid var(--primary);
        }

        .advantage-card h4 {
            font-size: 1.3rem;
            margin-bottom: 1rem;
            color: var(--secondary);
        }

        .advantage-card ul {
            list-style: none;
        }

        .advantage-card li {
            margin-bottom: 1rem;
            position: relative;
            padding-left: 1.5rem;
        }

        .advantage-card li::before {
            content: '\f00c';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            left: 0;
            color: var(--primary);
        }

        .competitive-comparison {
            margin-top: 3rem;
        }

        .competitive-comparison h3 {
            font-size: 1.8rem;
            margin-bottom: 2rem;
            text-align: center;
            color: var(--secondary);
        }

        .comparison-table {
            overflow-x: auto;
            background-color: var(--light);
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .comparison-table table {
            width: 100%;
            border-collapse: collapse;
        }

        .comparison-table th,
        .comparison-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--light-grey);
        }

        .comparison-table th {
            background-color: var(--secondary);
            color: var(--light);
            font-weight: 600;
        }

        .comparison-table .rating {
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .comparison-table .rating.strong {
            background-color: rgba(40, 167, 69, 0.2);
            color: #28a745;
        }

        .comparison-table .rating.fair {
            background-color: rgba(255, 193, 7, 0.2);
            color: #ffc107;
        }

        .comparison-table .rating.poor {
            background-color: rgba(220, 53, 69, 0.2);
            color: #dc3545;
        }

        /* Enhanced SWOT Analysis Section */
        .swot-intro {
            text-align: center;
            max-width: 800px;
            margin: 0 auto 3rem;
            padding: 1.5rem;
            background-color: rgba(255, 107, 53, 0.1);
            border-radius: 10px;
            border-left: 4px solid var(--primary);
        }

        .swot-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .swot-card {
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .swot-card h3 {
            font-size: 1.5rem;
            margin-bottom: 1.5rem;
            position: relative;
            padding-bottom: 0.5rem;
        }

        .swot-card h3::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 3px;
        }

        .swot-content h4 {
            font-size: 1.2rem;
            margin: 1.5rem 0 1rem;
            color: var(--secondary);
        }

        .swot-content p {
            margin-bottom: 1rem;
            color: var(--grey);
            font-style: italic;
        }

        .strength {
            background-color: rgba(40, 167, 69, 0.1);
            border-left: 4px solid #28a745;
        }

        .strength h3::after {
            background-color: #28a745;
        }

        .weakness {
            background-color: rgba(220, 53, 69, 0.1);
            border-left: 4px solid #dc3545;
        }

        .weakness h3::after {
            background-color: #dc3545;
        }

        .opportunity {
            background-color: rgba(0, 123, 255, 0.1);
            border-left: 4px solid #007bff;
        }

        .opportunity h3::after {
            background-color: #007bff;
        }

        .threat {
            background-color: rgba(255, 193, 7, 0.1);
            border-left: 4px solid #ffc107;
        }

        .threat h3::after {
            background-color: #ffc107;
        }

        .swot-card ul {
            list-style: none;
        }

        .swot-card li {
            margin-bottom: 1rem;
            position: relative;
            padding-left: 1.5rem;
        }

        .swot-card li::before {
            content: '\f054';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            left: 0;
            color: var(--secondary);
        }

        .swot-actions {
            margin-top: 3rem;
        }

        .swot-actions h3 {
            font-size: 2rem;
            margin-bottom: 2rem;
            text-align: center;
            color: var(--secondary);
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
        }

        .action-card {
            background-color: var(--light);
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .weakness-action {
            border-left: 4px solid #dc3545;
        }

        .threat-action {
            border-left: 4px solid #ffc107;
        }

        .action-card h4 {
            font-size: 1.3rem;
            margin-bottom: 1.5rem;
            color: var(--secondary);
        }

        .action-item {
            margin-bottom: 2rem;
            padding-bottom: 1.5rem;
            border-bottom: 1px dashed var(--light-grey);
        }

        .action-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .action-item h5 {
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
            color: var(--primary);
        }

        .action-item p {
            margin-bottom: 0.8rem;
            color: var(--grey);
            font-weight: 500;
        }

        .action-item ul {
            list-style: none;
        }

        .action-item li {
            margin-bottom: 0.5rem;
            position: relative;
            padding-left: 1.2rem;
            font-size: 0.9rem;
        }

        .action-item li::before {
            content: '\f0da';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            left: 0;
            color: var(--primary);
        }

        /* Market Analysis Section */
        .market-analysis {
            background-color: var(--light);
        }

        .market-content {
            display: grid;
            gap: 3rem;
        }

        .market-overview h3 {
            font-size: 2rem;
            margin-bottom: 2rem;
            text-align: center;
            color: var(--secondary);
        }

        .market-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        .stat-card {
            background-color: var(--light);
            padding: 2rem;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            border-left: 4px solid var(--primary);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card h4 {
            font-size: 1rem;
            margin-bottom: 1rem;
            color: var(--grey);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .stat-value {
            display: block;
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 0.5rem;
        }

        .stat-card p {
            color: var(--grey);
            font-size: 0.9rem;
        }

        .market-drivers h4 {
            font-size: 1.5rem;
            margin-bottom: 2rem;
            text-align: center;
            color: var(--secondary);
        }

        .drivers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }

        .driver-item {
            background-color: var(--light);
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease;
        }

        .driver-item:hover {
            transform: translateY(-3px);
        }

        .driver-item i {
            font-size: 2.5rem;
            color: var(--primary);
            margin-bottom: 1rem;
        }

        .driver-item h5 {
            font-size: 1.1rem;
            margin-bottom: 1rem;
            color: var(--secondary);
        }

        .driver-item p {
            color: var(--grey);
            font-size: 0.9rem;
        }

        .customer-demand h3 {
            font-size: 2rem;
            margin-bottom: 2rem;
            text-align: center;
            color: var(--secondary);
        }

        .demand-analysis {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
        }

        .demand-section {
            background-color: var(--light);
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .demand-section h4 {
            font-size: 1.3rem;
            margin-bottom: 1.5rem;
            color: var(--secondary);
            border-bottom: 2px solid var(--primary);
            padding-bottom: 0.5rem;
        }

        .research-stats {
            display: grid;
            gap: 1rem;
        }

        .research-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background-color: rgba(255, 107, 53, 0.05);
            border-radius: 5px;
        }

        .research-item .percentage {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary);
            min-width: 60px;
        }

        .research-item p {
            color: var(--grey);
            margin: 0;
        }

        .validation-list {
            list-style: none;
        }

        .validation-list li {
            margin-bottom: 1rem;
            position: relative;
            padding-left: 1.5rem;
        }

        .validation-list li::before {
            content: '\f058';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            left: 0;
            color: var(--primary);
        }

        .target-segments h3 {
            font-size: 2rem;
            margin-bottom: 2rem;
            text-align: center;
            color: var(--secondary);
        }

        .segments-grid {
            display: grid;
            gap: 2rem;
        }

        .segment-card {
            background-color: var(--light);
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease;
        }

        .segment-card:hover {
            transform: translateY(-3px);
        }

        .segment-card.primary {
            border-left: 4px solid #28a745;
        }

        .segment-card.secondary {
            border-left: 4px solid #007bff;
        }

        .segment-card.tertiary {
            border-left: 4px solid #ffc107;
        }

        .segment-card h4 {
            font-size: 1.3rem;
            margin-bottom: 1rem;
            color: var(--secondary);
        }

        .segment-details p {
            margin-bottom: 0.8rem;
            color: var(--grey);
        }

        .segment-details ul {
            list-style: none;
            margin-top: 1rem;
        }

        .segment-details li {
            margin-bottom: 0.5rem;
            position: relative;
            padding-left: 1.2rem;
            font-size: 0.9rem;
            color: var(--grey);
        }

        .segment-details li::before {
            content: '\f054';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            left: 0;
            color: var(--primary);
            font-size: 0.7rem;
        }

        /* Enhanced Marketing Plan Section */
        .marketing {
            background-color: var(--light-grey);
        }

        .marketing-content {
            display: grid;
            gap: 3rem;
        }

        .marketing-objectives h3,
        .marketing-strategy h3,
        .marketing-budget h3,
        .distribution-strategy h3 {
            font-size: 1.8rem;
            margin-bottom: 2rem;
            color: var(--secondary);
            text-align: center;
        }

        .objectives-timeline {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .objective-item {
            background-color: var(--light);
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            border-left: 4px solid var(--primary);
        }

        .objective-item h4 {
            font-size: 1.2rem;
            margin-bottom: 1rem;
            color: var(--primary);
        }

        .objective-item ul {
            list-style: none;
        }

        .objective-item li {
            margin-bottom: 0.8rem;
            position: relative;
            padding-left: 1.5rem;
        }

        .objective-item li::before {
            content: '\f00c';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            left: 0;
            color: var(--primary);
        }

        .strategy-channels {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
        }

        .channel-card {
            background-color: var(--light);
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease;
        }

        .channel-card:hover {
            transform: translateY(-3px);
        }

        .channel-card h4 {
            font-size: 1.2rem;
            margin-bottom: 1.5rem;
            color: var(--secondary);
            border-bottom: 2px solid var(--primary);
            padding-bottom: 0.5rem;
        }

        .channel-card ul {
            list-style: none;
        }

        .channel-card li {
            margin-bottom: 1rem;
            position: relative;
            padding-left: 1.5rem;
        }

        .channel-card li::before {
            content: '\f054';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            left: 0;
            color: var(--primary);
        }

        .budget-breakdown {
            display: grid;
            gap: 2rem;
        }

        .budget-category {
            background-color: var(--light);
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .budget-category h4 {
            font-size: 1.2rem;
            margin-bottom: 1.5rem;
            color: var(--secondary);
            border-bottom: 2px solid var(--primary);
            padding-bottom: 0.5rem;
        }

        .budget-items {
            display: grid;
            gap: 1rem;
        }

        .budget-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background-color: rgba(255, 107, 53, 0.05);
            border-radius: 5px;
            border-left: 3px solid var(--primary);
        }

        .budget-item .amount {
            font-weight: 700;
            color: var(--primary);
            min-width: 100px;
            font-size: 1.1rem;
        }

        .budget-item .category {
            color: var(--grey);
        }

        .distribution-channels {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .channel-strategy {
            background-color: var(--light);
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            border-left: 4px solid var(--primary);
        }

        .channel-strategy h4 {
            font-size: 1.2rem;
            margin-bottom: 1rem;
            color: var(--secondary);
        }

        .channel-strategy ul {
            list-style: none;
        }

        .channel-strategy li {
            margin-bottom: 1rem;
            position: relative;
            padding-left: 1.5rem;
        }

        .channel-strategy li::before {
            content: '\f0da';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            left: 0;
            color: var(--primary);
        }

        /* Risk Analysis Section */
        .risk-analysis {
            background-color: var(--light);
        }

        .risk-content {
            display: grid;
            gap: 3rem;
        }

        .risk-overview {
            text-align: center;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background-color: rgba(220, 53, 69, 0.1);
            border-radius: 10px;
            border-left: 4px solid #dc3545;
        }

        .risk-overview h3 {
            font-size: 1.8rem;
            margin-bottom: 1rem;
            color: var(--secondary);
        }

        .risk-matrix h3 {
            font-size: 2rem;
            margin-bottom: 2rem;
            text-align: center;
            color: var(--secondary);
        }

        .risks-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
        }

        .risk-card {
            background-color: var(--light);
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .risk-card.high-high {
            border-left: 4px solid #dc3545;
            background-color: rgba(220, 53, 69, 0.05);
        }

        .risk-card.high-low {
            border-left: 4px solid #fd7e14;
            background-color: rgba(253, 126, 20, 0.05);
        }

        .risk-card.low-high {
            border-left: 4px solid #ffc107;
            background-color: rgba(255, 193, 7, 0.05);
        }

        .risk-card.low-low {
            border-left: 4px solid #28a745;
            background-color: rgba(40, 167, 69, 0.05);
        }

        .risk-card h4 {
            font-size: 1.2rem;
            margin-bottom: 1.5rem;
            color: var(--secondary);
            text-align: center;
            border-bottom: 2px solid var(--primary);
            padding-bottom: 0.5rem;
        }

        .risk-item {
            margin-bottom: 2rem;
            padding-bottom: 1.5rem;
            border-bottom: 1px dashed var(--light-grey);
        }

        .risk-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .risk-item h5 {
            font-size: 1.1rem;
            margin-bottom: 1rem;
            color: var(--primary);
        }

        .risk-item p {
            margin-bottom: 0.5rem;
            color: var(--grey);
            font-size: 0.9rem;
        }

        .contingency-plans h3 {
            font-size: 2rem;
            margin-bottom: 2rem;
            text-align: center;
            color: var(--secondary);
        }

        .contingency-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
        }

        .contingency-card {
            background-color: var(--light);
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            border-left: 4px solid var(--primary);
        }

        .contingency-card h4 {
            font-size: 1.3rem;
            margin-bottom: 1.5rem;
            color: var(--secondary);
        }

        .scenario h5 {
            font-size: 1.1rem;
            margin-bottom: 1rem;
            color: var(--primary);
        }

        .scenario p {
            margin-bottom: 1rem;
            color: var(--grey);
        }

        .scenario ul {
            list-style: none;
        }

        .scenario li {
            margin-bottom: 0.8rem;
            position: relative;
            padding-left: 1.5rem;
            color: var(--grey);
        }

        .scenario li::before {
            content: '\f054';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            left: 0;
            color: var(--primary);
        }

        .risk-monitoring h3 {
            font-size: 2rem;
            margin-bottom: 2rem;
            text-align: center;
            color: var(--secondary);
        }

        .monitoring-framework {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
        }

        .monitoring-section {
            background-color: var(--light);
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            border-left: 4px solid var(--primary);
        }

        .monitoring-section h4 {
            font-size: 1.3rem;
            margin-bottom: 1.5rem;
            color: var(--secondary);
        }

        .kri-list {
            display: grid;
            gap: 1rem;
        }

        .kri-item {
            display: grid;
            grid-template-columns: 1fr auto auto;
            gap: 1rem;
            padding: 1rem;
            background-color: rgba(255, 107, 53, 0.05);
            border-radius: 5px;
            align-items: center;
        }

        .kri-metric {
            font-weight: 600;
            color: var(--secondary);
        }

        .kri-threshold {
            font-weight: 600;
            color: #dc3545;
            font-size: 0.9rem;
        }

        .kri-action {
            font-size: 0.8rem;
            color: var(--grey);
            text-align: right;
        }

        .kri-detailed-list {
            display: grid;
            gap: 2rem;
        }

        .kri-detailed-item {
            background-color: var(--light);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            border-left: 4px solid var(--primary);
        }

        .kri-header {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 1rem;
            padding: 1.5rem;
            background-color: rgba(255, 107, 53, 0.1);
            align-items: center;
        }

        .kri-header .kri-metric {
            font-weight: 600;
            color: var(--secondary);
            font-size: 1.1rem;
        }

        .kri-header .kri-threshold {
            font-weight: 600;
            color: #dc3545;
            background-color: rgba(220, 53, 69, 0.1);
            padding: 0.5rem 1rem;
            border-radius: 15px;
            font-size: 0.9rem;
        }

        .kri-mitigation {
            padding: 1.5rem;
        }

        .kri-mitigation h5 {
            font-size: 1rem;
            margin: 1.5rem 0 1rem 0;
            color: var(--primary);
            border-bottom: 1px solid var(--light-grey);
            padding-bottom: 0.5rem;
        }

        .kri-mitigation h5:first-child {
            margin-top: 0;
        }

        .kri-mitigation ol {
            margin-left: 1.5rem;
            color: var(--grey);
        }

        .kri-mitigation li {
            margin-bottom: 0.8rem;
            line-height: 1.5;
        }

        .review-schedule {
            display: grid;
            gap: 1.5rem;
        }

        .review-item {
            padding: 1rem;
            background-color: rgba(255, 107, 53, 0.05);
            border-radius: 5px;
            border-left: 3px solid var(--primary);
        }

        .review-item h5 {
            font-size: 1rem;
            margin-bottom: 0.5rem;
            color: var(--primary);
        }

        .review-item p {
            color: var(--grey);
            font-size: 0.9rem;
            margin: 0;
        }

        /* Financial Projections Section */
        .financial-content {
            display: grid;
            gap: 3rem;
        }

        .cost-items,
        .projection-items,
        .pricing-items {
            display: grid;
            gap: 1rem;
        }

        .cost-item,
        .projection-item,
        .pricing-item {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background-color: var(--light);
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        .cost-item.total {
            background-color: rgba(255, 107, 53, 0.1);
            border-left: 4px solid var(--primary);
        }

        .cost-explanation {
            margin-bottom: 2rem;
            padding: 1.5rem;
            background-color: rgba(40, 167, 69, 0.1);
            border-radius: 10px;
            border-left: 4px solid #28a745;
        }

        .cost-explanation p {
            margin: 0;
            color: var(--grey);
            font-style: italic;
        }

        .existing-resources {
            margin-top: 2rem;
            padding: 2rem;
            background-color: rgba(0, 123, 255, 0.1);
            border-radius: 10px;
            border-left: 4px solid #007bff;
        }

        .existing-resources h4 {
            font-size: 1.2rem;
            margin-bottom: 1.5rem;
            color: var(--secondary);
        }

        .resource-items {
            display: grid;
            gap: 1rem;
        }

        .resource-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background-color: var(--light);
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        .resource-type {
            font-weight: 600;
            color: var(--secondary);
        }

        .resource-value {
            color: var(--grey);
            font-style: italic;
        }

        .cost-item .amount,
        .projection-item .amount,
        .pricing-item .price {
            font-weight: 700;
            color: var(--primary);
            min-width: 120px;
        }

        .revenue-breakdown {
            display: grid;
            gap: 2rem;
        }

        .revenue-source {
            background-color: var(--light);
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .revenue-source.lumerous {
            border-left: 4px solid var(--primary);
        }

        .revenue-source.additional {
            border-left: 4px solid #28a745;
        }

        .revenue-total {
            background-color: var(--secondary);
            color: var(--light);
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .revenue-source h4,
        .revenue-total h4 {
            font-size: 1.3rem;
            margin-bottom: 1.5rem;
            color: var(--secondary);
        }

        .revenue-total h4 {
            color: var(--primary);
        }

        .projection-item.total {
            background-color: rgba(255, 107, 53, 0.1);
            border-left: 4px solid var(--primary);
        }

        .lumerous-pricing {
            background-color: rgba(255, 107, 53, 0.1);
            border-left: 4px solid var(--primary);
        }

        /* Profit Allocation Strategy */
        .profit-allocation-strategy {
            margin-top: 3rem;
            padding: 3rem;
            background-color: rgba(255, 107, 53, 0.05);
            border-radius: 15px;
            border: 2px solid rgba(255, 107, 53, 0.1);
        }

        .profit-allocation-strategy h3 {
            font-size: 2rem;
            margin-bottom: 2rem;
            text-align: center;
            color: var(--secondary);
        }

        .allocation-intro {
            text-align: center;
            max-width: 800px;
            margin: 0 auto 3rem;
            padding: 1.5rem;
            background-color: var(--light);
            border-radius: 10px;
            border-left: 4px solid var(--primary);
        }

        .allocation-intro p {
            color: var(--grey);
            font-size: 1.1rem;
            margin: 0;
        }

        .allocation-breakdown {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .allocation-card {
            background-color: var(--light);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease;
        }

        .allocation-card:hover {
            transform: translateY(-5px);
        }

        .allocation-header {
            padding: 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: white;
        }

        .allocation-card.contingency .allocation-header {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }

        .allocation-card.rd .allocation-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
        }

        .allocation-card.growth .allocation-header {
            background: linear-gradient(135deg, #28a745, #1e7e34);
        }

        .allocation-card.culture .allocation-header {
            background: linear-gradient(135deg, #ffc107, #e0a800);
        }

        .allocation-card.passive .allocation-header {
            background: linear-gradient(135deg, #6f42c1, #5a32a3);
        }

        .allocation-header h4 {
            font-size: 1.2rem;
            margin: 0;
        }

        .allocation-percentage {
            font-size: 1.5rem;
            font-weight: 700;
            background-color: rgba(255, 255, 255, 0.2);
            padding: 0.5rem 1rem;
            border-radius: 20px;
        }

        .allocation-content {
            padding: 1.5rem;
        }

        .allocation-content p {
            margin-bottom: 1rem;
            color: var(--grey);
            font-weight: 500;
        }

        .allocation-content ul {
            list-style: none;
            margin-bottom: 1.5rem;
        }

        .allocation-content li {
            margin-bottom: 0.8rem;
            position: relative;
            padding-left: 1.5rem;
            color: var(--grey);
        }

        .allocation-content li::before {
            content: '\f054';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            left: 0;
            color: var(--primary);
        }

        .allocation-cap {
            background-color: rgba(220, 53, 69, 0.1);
            padding: 1rem;
            border-radius: 8px;
            border-left: 3px solid #dc3545;
        }

        .allocation-cap h5 {
            font-size: 1rem;
            margin-bottom: 0.5rem;
            color: #dc3545;
        }

        .allocation-cap p {
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .projection-item .year {
            font-weight: 700;
            color: var(--secondary);
            min-width: 80px;
        }

        .projection-item .details,
        .pricing-item .details {
            width: 100%;
            font-size: 0.9rem;
            color: var(--grey);
            margin-top: 0.5rem;
        }

        /* Milestones Section */
        .timeline {
            position: relative;
            max-width: 800px;
            margin: 3rem auto 0;
        }

        .timeline::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 2px;
            height: 100%;
            background-color: var(--primary);
        }

        .timeline-item {
            position: relative;
            margin-bottom: 3rem;
            width: 100%;
        }

        .timeline-item:nth-child(odd) {
            padding-right: calc(50% + 2rem);
            text-align: right;
        }

        .timeline-item:nth-child(even) {
            padding-left: calc(50% + 2rem);
            text-align: left;
        }

        .timeline-date {
            position: absolute;
            top: 0;
            width: 120px;
            padding: 0.5rem;
            background-color: var(--primary);
            color: var(--light);
            border-radius: 5px;
            font-weight: 600;
            text-align: center;
        }

        .timeline-item:nth-child(odd) .timeline-date {
            right: calc(50% + 2rem);
            transform: translateX(50%);
        }

        .timeline-item:nth-child(even) .timeline-date {
            left: calc(50% + 2rem);
            transform: translateX(-50%);
        }

        .timeline-content {
            padding: 1.5rem;
            background-color: var(--light);
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .timeline-content h3 {
            color: var(--primary);
            margin-bottom: 0.5rem;
        }

        /* Footer */
        footer {
            background-color: var(--secondary);
            color: var(--light);
            padding: 4rem 2rem 2rem;
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 3rem;
        }

        .footer-column h3 {
            font-size: 1.5rem;
            margin-bottom: 1.5rem;
            color: var(--primary);
        }

        .footer-column p, .footer-column a {
            opacity: 0.8;
            margin-bottom: 1rem;
            display: block;
            color: var(--light);
            text-decoration: none;
            transition: opacity 0.3s ease;
        }

        .footer-column a:hover {
            opacity: 1;
            color: var(--primary);
        }

        .footer-column i {
            width: 20px;
            margin-right: 0.5rem;
            text-align: center;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 2rem;
            margin-top: 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            opacity: 0.7;
        }

        /* Responsive Styles */
        @media (max-width: 992px) {
            .about-content,
            .lumerous-content {
                flex-direction: column;
            }
            
            .about-image,
            .lumerous-image {
                order: -1;
                max-width: 600px;
                margin: 0 auto;
            }

            .timeline::before {
                left: 2rem;
            }

            .timeline-item:nth-child(odd),
            .timeline-item:nth-child(even) {
                padding-left: 4rem;
                padding-right: 2rem;
                text-align: left;
            }

            .timeline-item:nth-child(odd) .timeline-date,
            .timeline-item:nth-child(even) .timeline-date {
                left: 0;
                right: auto;
                transform: none;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                width: 280px;
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .sidebar-toggle {
                display: block;
            }

            .main-content {
                margin-left: 0;
                margin-top: 80px;
            }

            .hero h1 {
                font-size: 2.5rem;
            }

            .hero p {
                font-size: 1rem;
            }

            .btn {
                padding: 0.6rem 1.5rem;
            }

            .section-title h2 {
                font-size: 2rem;
            }

            .services-grid,
            .team-grid,
            .swot-grid,
            .highlight-grid,
            .value-points,
            .strategy-channels,
            .segments-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 576px) {
            .hero h1 {
                font-size: 2rem;
            }

            .btn-container {
                display: flex;
                flex-direction: column;
                gap: 1rem;
            }

            .btn-outline {
                margin-left: 0;
            }

            .swot-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <header id="header">
        <nav>
            <a href="#" class="logo">Solving<span>Tomorrow</span></a>
            <button class="sidebar-toggle" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>
        </nav>
    </header>

    <!-- Left Sidebar Navigation -->
    <aside class="sidebar" id="sidebar">
        <div class="sidebar-content">
            <h3>Business Plan</h3>
            <ul class="nav-tabs">
                <li class="nav-tab"><a href="#configurator" class="nav-link">📊 Business Configurator</a></li>
                <li class="nav-tab"><a href="#executive-summary" class="nav-link">Executive Summary</a></li>
                <li class="nav-tab"><a href="#services" class="nav-link">Services</a></li>
                <li class="nav-tab"><a href="#lumerous" class="nav-link">Core Platform</a></li>
                <li class="nav-tab"><a href="#team" class="nav-link">Our Team</a></li>
                <li class="nav-tab"><a href="#revenue" class="nav-link">Revenue Model</a></li>
                <li class="nav-tab"><a href="#competitors" class="nav-link">Competitor Analysis</a></li>
                <li class="nav-tab"><a href="#swot" class="nav-link">SWOT Analysis</a></li>
                <li class="nav-tab"><a href="#market" class="nav-link">Market Analysis</a></li>
                <li class="nav-tab"><a href="#marketing" class="nav-link">Marketing Plan</a></li>
                <li class="nav-tab"><a href="#risk" class="nav-link">Risk Analysis</a></li>
                <li class="nav-tab"><a href="#implementation" class="nav-link">Implementation Timeline</a></li>
                <li class="nav-tab"><a href="#financial" class="nav-link">Financial Projections</a></li>
                <li class="nav-tab"><a href="#milestones" class="nav-link">Key Milestones</a></li>
                <li class="nav-tab"><a href="#contact" class="nav-link">Contact</a></li>
                <li class="nav-tab"><a href="#appendixes" class="nav-link">Appendixes</a></li>
            </ul>

            <h3 style="margin-top: 30px;">Detailed Analysis</h3>
            <ul class="nav-tabs">
                <li class="nav-tab"><a href="market-analysis.html" class="nav-link" target="_blank">📈 Market Analysis</a></li>
                <li class="nav-tab"><a href="product-roadmap.html" class="nav-link" target="_blank">🗺️ Product Roadmap</a></li>
                <li class="nav-tab"><a href="team.html" class="nav-link" target="_blank">👥 Team Profiles</a></li>
                <li class="nav-tab"><a href="revenue-model.html" class="nav-link" target="_blank">💰 Revenue Model</a></li>
            </ul>
        </div>
    </aside>

    <!-- Main Content Area -->
    <main class="main-content" id="mainContent">

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-content">
            <h1><span class="dynamic-content" data-field="productCategory">AI-Powered SaaS Solutions</span> for <span>South African Businesses</span></h1>
            <p><span class="dynamic-content" data-field="productTagline">Specializing in compliance automation, e-commerce, and education technology with cutting-edge AI solutions.</span></p>
            <div class="btn-container">
                <a href="#executive-summary" class="btn">View Business Plan</a>
                <a href="#revenue" class="btn btn-outline">Revenue Projections</a>
            </div>

        </div>
    </section>

    <!-- Business Configurator Section -->
    <section class="section configurator" id="configurator">
        <div class="section-title">
            <h2>Business Plan <span>Configurator</span></h2>
            <p>Customize this business plan for your tech product</p>
        </div>

        <div class="configurator-content">
            <div class="config-intro">
                <h3>🎯 Customize Your Business Plan</h3>
                <p>Adjust the key parameters below to automatically update all financial projections, timelines, and product-specific content throughout the business plan.</p>
            </div>

            <div class="config-grid">
                <!-- Product Configuration -->
                <div class="config-card">
                    <h4>📱 Product Configuration</h4>
                    <div class="config-group">
                        <label for="productName">Product Name:</label>
                        <input type="text" id="productName" value="Lumerous" placeholder="Enter product name">
                    </div>
                    <div class="config-group">
                        <label for="productType">Product Type:</label>
                        <select id="productType">
                            <option value="education">Education Platform</option>
                            <option value="ecommerce">E-commerce Solution</option>
                            <option value="compliance">Compliance Software</option>
                            <option value="productivity">Productivity Tool</option>
                            <option value="analytics">Analytics Platform</option>
                            <option value="communication">Communication Tool</option>
                            <option value="custom">Custom Software</option>
                        </select>
                    </div>
                    <div class="config-group">
                        <label for="targetMarket">Target Market:</label>
                        <select id="targetMarket">
                            <option value="education">Education Sector</option>
                            <option value="smb">Small-Medium Business</option>
                            <option value="enterprise">Enterprise</option>
                            <option value="healthcare">Healthcare</option>
                            <option value="finance">Financial Services</option>
                            <option value="retail">Retail</option>
                            <option value="government">Government</option>
                        </select>
                    </div>
                </div>

                <!-- Financial Configuration -->
                <div class="config-card">
                    <h4>💰 Financial Parameters</h4>
                    <div class="config-group">
                        <label for="pricePerUser">Price per User/Month (R):</label>
                        <input type="number" id="pricePerUser" value="200" min="50" max="2000" step="10">
                    </div>
                    <div class="config-group">
                        <label for="monthlyCosts">Monthly Operating Costs (R):</label>
                        <input type="number" id="monthlyCosts" value="300000" min="50000" max="1000000" step="10000">
                    </div>
                    <div class="config-group">
                        <label for="initialCapital">Initial Capital (R):</label>
                        <input type="number" id="initialCapital" value="80000" min="20000" max="500000" step="5000">
                    </div>
                    <div class="config-group">
                        <label for="growthRate">Monthly User Growth (%):</label>
                        <input type="number" id="growthRate" value="15" min="5" max="50" step="1">
                    </div>
                </div>

                <!-- Market Configuration -->
                <div class="config-card">
                    <h4>📊 Market Parameters</h4>
                    <div class="config-group">
                        <label for="marketSize">Total Addressable Market (R):</label>
                        <input type="number" id="marketSize" value="2800000000" min="100000000" max="10000000000" step="100000000">
                    </div>
                    <div class="config-group">
                        <label for="competitorCount">Number of Main Competitors:</label>
                        <input type="number" id="competitorCount" value="3" min="1" max="10" step="1">
                    </div>
                    <div class="config-group">
                        <label for="marketGrowth">Annual Market Growth (%):</label>
                        <input type="number" id="marketGrowth" value="15" min="5" max="30" step="1">
                    </div>
                </div>

                <!-- Timeline Configuration -->
                <div class="config-card">
                    <h4>⏱️ Timeline Parameters</h4>
                    <div class="config-group">
                        <label for="breakevenMonth">Target Breakeven Month:</label>
                        <input type="number" id="breakevenMonth" value="5" min="3" max="18" step="1">
                    </div>
                    <div class="config-group">
                        <label for="developmentTime">Development Time (Months):</label>
                        <input type="number" id="developmentTime" value="4" min="2" max="12" step="1">
                    </div>
                    <div class="config-group">
                        <label for="launchDelay">Launch Delay Buffer (Months):</label>
                        <input type="number" id="launchDelay" value="1" min="0" max="6" step="1">
                    </div>
                </div>
            </div>

            <div class="config-actions">
                <button id="updatePlan" class="btn">🔄 Update Business Plan</button>
                <button id="exportPDF" class="btn">📄 Export to PDF</button>
                <button id="resetDefaults" class="btn btn-outline">↩️ Reset to Defaults</button>
                <button id="exportConfig" class="btn btn-outline">📥 Export Configuration</button>
            </div>

            <div class="config-results" id="configResults" style="display: none;">
                <h4>📈 Updated Calculations</h4>
                <div class="results-grid">
                    <div class="result-item">
                        <span class="result-label">Breakeven Users:</span>
                        <span class="result-value" id="breakevenUsers">-</span>
                    </div>
                    <div class="result-item">
                        <span class="result-label">Year 1 Revenue:</span>
                        <span class="result-value" id="year1Revenue">-</span>
                    </div>
                    <div class="result-item">
                        <span class="result-label">Year 2 Revenue:</span>
                        <span class="result-value" id="year2Revenue">-</span>
                    </div>
                    <div class="result-item">
                        <span class="result-label">Market Share (Year 2):</span>
                        <span class="result-value" id="marketShare">-</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Executive Summary Section -->
    <section class="section executive-summary" id="executive-summary">
        <div class="section-title">
            <h2>Executive <span>Summary</span></h2>
            <p>Comprehensive overview of Solving Tomorrow's business opportunity</p>
        </div>

        <div class="executive-content">
            <div class="executive-overview">
                <h3>Business Overview</h3>
                <p><strong>Solving Tomorrow (Pty) Ltd</strong> is a Durban-based tech startup developing <strong><span class="dynamic-content" data-field="productName">Lumerous</span></strong>, <span class="dynamic-content" data-field="productDescription">an AI-powered learning platform specifically designed for the South African education market</span>. Founded in 2025, we address the critical gap in <span class="dynamic-content" data-field="targetSegment">students, teachers, and educational institutions</span> with our innovative solution.</p>

                <div class="key-highlights">
                    <div class="highlight-grid">
                        <div class="highlight-card">
                            <h4>Market Opportunity</h4>
                            <ul>
                                <li><span class="dynamic-content" data-field="marketSize">R2.8 billion</span> South African market</li>
                                <li>15% annual growth rate</li>
                                <li>Targeting <span class="dynamic-content" data-field="targetSegment">students, teachers, and educational institutions</span></li>
                                <li>Post-COVID digital transformation acceleration</li>
                            </ul>
                        </div>
                        <div class="highlight-card">
                            <h4>Competitive Advantage</h4>
                            <ul id="advantages-list">
                                <li>100% CAPS/IEB curriculum alignment</li>
                                <li>Advanced AI tutoring in local languages</li>
                                <li>40% lower pricing than competitors</li>
                                <li>Mobile-first design for SA market</li>
                            </ul>
                        </div>
                        <div class="highlight-card">
                            <h4>Financial Projections</h4>
                            <ul>
                                <li>Breakeven: 1,500 users (Month 5 with <span class="dynamic-content" data-field="productName">Lumerous</span>)</li>
                                <li>Year 1 Revenue: R4.32 million (<span class="dynamic-content" data-field="productName">Lumerous</span> + other services)</li>
                                <li>Year 2 Revenue: R7.2 million</li>
                                <li>Year 3 Revenue: R14.4 million</li>
                            </ul>
                        </div>
                        <div class="highlight-card">
                            <h4>Bootstrap Strategy</h4>
                            <ul>
                                <li>Initial Capital: <span class="dynamic-content" data-field="initialCapital">R80,000</span> (self-funded)</li>
                                <li>Existing Assets: Hardware & equipment owned</li>
                                <li>Funding Strategy: 100% bootstrapped, no external funding</li>
                                <li>Growth Model: Organic growth through revenue reinvestment</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="value-proposition">
                <h3>Unique Value Proposition</h3>
                <div class="value-points">
                    <div class="value-point">
                        <i class="fas fa-brain"></i>
                        <h4>AI-Powered Personalization</h4>
                        <p>Advanced machine learning algorithms provide personalized learning paths, adaptive assessments, and intelligent tutoring available 24/7 in multiple South African languages.</p>
                    </div>
                    <div class="value-point">
                        <i class="fas fa-graduation-cap"></i>
                        <h4>Curriculum-Perfect Alignment</h4>
                        <p>100% alignment with CAPS and IEB curricula, developed by former Department of Basic Education specialists with deep understanding of local educational requirements.</p>
                    </div>
                    <div class="value-point">
                        <i class="fas fa-mobile-alt"></i>
                        <h4>Mobile-First Accessibility</h4>
                        <p>Designed for South African connectivity constraints with offline capabilities, data-light operation, and partnerships for zero-rated access.</p>
                    </div>
                    <div class="value-point">
                        <i class="fas fa-chart-line"></i>
                        <h4>Proven Learning Outcomes</h4>
                        <p>Pilot programs show 23% improvement in test scores, with 94% teacher satisfaction and strong student engagement metrics.</p>
                    </div>
                </div>
            </div>

            <div class="business-model-summary">
                <h3>Business Model</h3>
                <div class="model-overview">
                    <div class="revenue-streams">
                        <h4>Revenue Streams</h4>
                        <div class="stream-item">
                            <span class="stream-name">Individual Subscriptions</span>
                            <span class="stream-price dynamic-content" data-field="pricePerUser">R200/month</span>
                            <span class="stream-target">Target: 60% of revenue</span>
                        </div>
                        <div class="stream-item">
                            <span class="stream-name">Enterprise Licenses</span>
                            <span class="stream-price">R5,000/month</span>
                            <span class="stream-target">Target: 30% of revenue</span>
                        </div>
                        <div class="stream-item">
                            <span class="stream-name">Premium Features</span>
                            <span class="stream-price">R50-100/month</span>
                            <span class="stream-target">Target: 10% of revenue</span>
                        </div>
                    </div>

                    <div class="cost-structure">
                        <h4>Cost Structure</h4>
                        <div class="cost-breakdown">
                            <div class="cost-item">
                                <span class="cost-category">Cloud Infrastructure</span>
                                <span class="cost-amount">R120,000/year</span>
                            </div>
                            <div class="cost-item">
                                <span class="cost-category">Team Salaries</span>
                                <span class="cost-amount">R2,400,000/year</span>
                            </div>
                            <div class="cost-item">
                                <span class="cost-category">Marketing & Sales</span>
                                <span class="cost-amount">R600,000/year</span>
                            </div>
                            <div class="cost-item">
                                <span class="cost-category">Operations & Legal</span>
                                <span class="cost-amount">R480,000/year</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bootstrap-strategy">
                <h3>Bootstrap Growth Strategy</h3>
                <div class="bootstrap-details">
                    <div class="bootstrap-approach">
                        <h4>Self-Funded Growth Model</h4>
                        <p>We're building Solving Tomorrow through <strong>100% bootstrapped growth</strong> - no external funding, no investors, no debt. This approach gives us complete control over our destiny and proves the strength of our business model.</p>

                        <div class="bootstrap-advantages">
                            <h5>Bootstrap Advantages</h5>
                            <div class="advantage-items">
                                <div class="advantage-item">
                                    <span class="advantage-icon">🎯</span>
                                    <span class="advantage-title">Complete Control</span>
                                    <span class="advantage-desc">100% ownership, all decisions ours</span>
                                </div>
                                <div class="advantage-item">
                                    <span class="advantage-icon">💪</span>
                                    <span class="advantage-title">Lean Operations</span>
                                    <span class="advantage-desc">Forced efficiency, no waste</span>
                                </div>
                                <div class="advantage-item">
                                    <span class="advantage-icon">🚀</span>
                                    <span class="advantage-title">Proven Demand</span>
                                    <span class="advantage-desc">Revenue validates market need</span>
                                </div>
                                <div class="advantage-item">
                                    <span class="advantage-icon">🔒</span>
                                    <span class="advantage-title">No Dilution</span>
                                    <span class="advantage-desc">All profits belong to founders</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="growth-milestones">
                        <h4>Bootstrap Growth Milestones</h4>
                        <div class="milestone-timeline">
                            <div class="milestone-item">
                                <h5>Month 5</h5>
                                <p>Achieve breakeven with R80,000 investment, 1,500 users, R300K monthly revenue</p>
                            </div>
                            <div class="milestone-item">
                                <h5>Month 12</h5>
                                <p>Scale to 3,000+ users, R600K monthly revenue, begin profit allocation</p>
                            </div>
                            <div class="milestone-item">
                                <h5>Year 2</h5>
                                <p>R7.2M annual revenue, strong contingency fund, diversified income streams</p>
                            </div>
                            <div class="milestone-item">
                                <h5>Year 3+</h5>
                                <p>Market leadership, passive income portfolio, potential exit opportunities</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="section services" id="services">
        <div class="section-title">
            <h2>Our <span>Services</span></h2>
            <p>Comprehensive solutions tailored for South African businesses</p>
        </div>
        <div class="services-grid">
            <!-- Service 1 -->
            <div class="service-card">
                <div class="service-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div class="service-content">
                    <h3>POPIA Compliance SaaS</h3>
                    <p>AI-driven platform to automate compliance with South Africa's Protection of Personal Information Act.</p>
                    <ul class="service-features">
                        <li>Data audit and classification</li>
                        <li>Automated compliance reporting</li>
                        <li>Real-time monitoring and alerts</li>
                        <li>Subscription-based pricing</li>
                    </ul>
                </div>
            </div>
            
            <!-- Service 2 -->
            <div class="service-card">
                <div class="service-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="service-content">
                    <h3>E-commerce Solutions</h3>
                    <p>Custom e-commerce websites with AI-driven product recommendations and secure payment gateways.</p>
                    <ul class="service-features">
                        <li>User-friendly interfaces</li>
                        <li>AI product recommendations</li>
                        <li>Monthly maintenance plans</li>
                        <li>SEO optimization</li>
                    </ul>
                </div>
            </div>
            
            <!-- Service 3 -->
            <div class="service-card">
                <div class="service-icon">
                    <i class="fas fa-laptop-code"></i>
                </div>
                <div class="service-content">
                    <h3>IT Services</h3>
                    <p>Comprehensive hardware and networking solutions with ongoing maintenance contracts.</p>
                    <ul class="service-features">
                        <li>Hardware installation</li>
                        <li>Networking solutions</li>
                        <li>Custom PC builds</li>
                        <li>Monthly maintenance</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Lumerous Section -->
    <section class="section lumerous" id="lumerous">
        <div class="section-title">
            <h2><span class="dynamic-content" data-field="productName">Lumerous</span> <span class="dynamic-content" data-field="productCategory">Education Platform</span></h2>
            <p><span class="dynamic-content" data-field="productTagline">Revolutionizing South African education through AI</span></p>
        </div>
        <div class="lumerous-content">
            <div class="lumerous-text">
                <h3>CAPS/IEB-Aligned Learning Platform</h3>
                <p><span class="dynamic-content" data-field="productName">Lumerous</span> is our flagship technology product - <span class="dynamic-content" data-field="productDescription">a subscription-based e-learning platform designed specifically for the South African curriculum</span>. Our <span class="dynamic-content" data-field="serviceModel">subscription-based learning platform</span> serves <span class="dynamic-content" data-field="clientType">schools</span> with <span class="dynamic-content" data-field="implementationTime">3-6 months per institution</span> implementation timeline.</p>
                <p>With initial capital of R80,000 and existing hardware infrastructure, we're focused on cloud services and platform development. Our MVP is set to launch in July 2025.</p>
                <ul class="lumerous-features" id="features-list">
                    <li>AI-powered personalized tutoring</li>
                    <li>100% CAPS/IEB curriculum alignment</li>
                    <li>Progress tracking and analytics</li>
                    <li>Gamified learning experience</li>
                    <li><span class="dynamic-content" data-field="pricePerUser">R200/month</span> subscription with 12-month contract</li>
                    <li>Currently being piloted with 3 <span class="dynamic-content" data-field="clientType">schools</span></li>
                </ul>
                <a href="#revenue" class="btn">See Revenue Model</a>
            </div>
            <div class="lumerous-image">
                <img src="https://images.unsplash.com/photo-1588072432836-e10032774350?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1472&q=80" alt="Lumerous platform interface">
            </div>
        </div>
    </section>

    <!-- Team Section -->
    <section class="section team" id="team">
        <div class="section-title">
            <h2>Our <span>Dream Team</span></h2>
            <p>The brilliant minds behind Solving Tomorrow</p>
        </div>
        <div class="team-members">
            <!-- Team Member 1 -->
            <div class="team-member">
                <div class="member-image">
                    <img src="https://randomuser.me/api/portraits/women/43.jpg" alt="Navenshia">
                </div>
                <div class="member-info">
                    <h3>Navenshia</h3>
                    <span class="role">Junior Full Stack Developer</span>
                    <p>Passionate about creating intuitive user experiences and building scalable web applications with modern technologies.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fas fa-envelope"></i></a>
                    </div>
                </div>
            </div>

            <!-- Team Member 2 -->
            <div class="team-member">
                <div class="member-image">
                    <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Shelden">
                </div>
                <div class="member-info">
                    <h3>Shelden</h3>
                    <span class="role">CTO & Co-Founder</span>
                    <p>Technology visionary with expertise in AI, machine learning, and scalable system architecture. Leads technical strategy and platform development.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                        <a href="#"><i class="fab fa-github"></i></a>
                        <a href="#"><i class="fas fa-envelope"></i></a>
                    </div>
                </div>
            </div>

            <!-- Team Member 3 -->
            <div class="team-member">
                <div class="member-image">
                    <img src="https://randomuser.me/api/portraits/women/65.jpg" alt="Magenta">
                </div>
                <div class="member-info">
                    <h3>Magenta</h3>
                    <span class="role">Full Stack Developer</span>
                    <p>Expert in modern web technologies with a focus on performance optimization and clean, maintainable code.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                        <a href="#"><i class="fab fa-github"></i></a>
                        <a href="#"><i class="fas fa-envelope"></i></a>
                    </div>
                </div>
            </div>

            <!-- Team Member 4 -->
            <div class="team-member">
                <div class="member-image">
                    <img src="https://randomuser.me/api/portraits/men/75.jpg" alt="Ashish">
                </div>
                <div class="member-info">
                    <h3>Ashish</h3>
                    <span class="role">AI Engineer</span>
                    <p>Specializes in natural language processing and educational AI systems, focusing on creating personalized learning experiences.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                        <a href="#"><i class="fab fa-github"></i></a>
                        <a href="#"><i class="fas fa-envelope"></i></a>
                    </div>
                </div>
            </div>

            <!-- Team Member 5 -->
            <div class="team-member">
                <div class="member-image">
                    <img src="https://randomuser.me/api/portraits/women/33.jpg" alt="Rhea">
                </div>
                <div class="member-info">
                    <h3>Rhea</h3>
                    <span class="role">QA & Testing Lead</span>
                    <p>Ensures product quality through comprehensive testing strategies and automated testing frameworks.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                        <a href="#"><i class="fas fa-envelope"></i></a>
                    </div>
                </div>
            </div>

            <!-- Team Member 6 -->
            <div class="team-member">
                <div class="member-image">
                    <img src="https://randomuser.me/api/portraits/men/55.jpg" alt="Shivek">
                </div>
                <div class="member-info">
                    <h3>Shivek</h3>
                    <span class="role">Mobile Developer</span>
                    <p>Creates seamless mobile experiences with expertise in cross-platform development and mobile UI/UX optimization.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                        <a href="#"><i class="fab fa-github"></i></a>
                        <a href="#"><i class="fas fa-envelope"></i></a>
                    </div>
                </div>
            </div>

            <!-- Team Member 7 -->
            <div class="team-member">
                <div class="member-image">
                    <img src="https://randomuser.me/api/portraits/men/42.jpg" alt="Claude">
                </div>
                <div class="member-info">
                    <h3>Claude</h3>
                    <span class="role">AI Assistant & Content Specialist</span>
                    <p>AI-powered content creation and educational material development specialist with advanced natural language capabilities.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                        <a href="#"><i class="fab fa-github"></i></a>
                        <a href="#"><i class="fas fa-envelope"></i></a>
                    </div>
                </div>
            </div>

            <!-- Team Member 8 -->
            <div class="team-member">
                <div class="member-image">
                    <img src="https://randomuser.me/api/portraits/women/28.jpg" alt="Priyanka">
                </div>
                <div class="member-info">
                    <h3>Priyanka</h3>
                    <span class="role">UX/UI Designer & Marketing</span>
                    <p>Creates beautiful, user-centered designs and drives marketing strategies for growth and user engagement.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                        <a href="#"><i class="fab fa-behance"></i></a>
                        <a href="#"><i class="fas fa-envelope"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Revenue Model Section -->
    <section class="section revenue-model" id="revenue">
        <div class="section-title">
            <h2>Revenue <span>Model</span></h2>
            <p>Sustainable growth through multiple revenue streams</p>
        </div>
        <div class="revenue-content">
            <div class="revenue-intro">
                <p>Our financial model is built on recurring SaaS subscriptions with tiered pricing. With R80,000 initial capital focused on cloud services and R300,000 monthly fixed costs, we project breakeven at 1,500 active users (R300,000 monthly revenue). Explore our projections below.</p>

                <div class="breakeven-explanation">
                    <h4>Breakeven Strategy Explained</h4>
                    <div class="calculation-breakdown">
                        <div class="calc-item">
                            <span class="calc-label">Monthly Fixed Costs:</span>
                            <span class="calc-value dynamic-content" data-field="monthlyCosts">R300,000</span>
                        </div>
                        <div class="calc-item">
                            <span class="calc-label"><span class="dynamic-content" data-field="productName">Lumerous</span> Revenue per User:</span>
                            <span class="calc-value dynamic-content" data-field="pricePerUser">R200/month</span>
                        </div>
                        <div class="calc-item total">
                            <span class="calc-label"><span class="dynamic-content" data-field="productName">Lumerous</span> Breakeven:</span>
                            <span class="calc-value">Month 5 (1,500 users)</span>
                        </div>
                        <div class="calc-item">
                            <span class="calc-label">Additional Services:</span>
                            <span class="calc-value">Extra revenue beyond breakeven</span>
                        </div>
                    </div>
                    <p><strong>Strategy:</strong> <span class="dynamic-content" data-field="productName">Lumerous</span> alone will achieve <span class="dynamic-content" data-field="monthlyCosts">R300,000/month</span> by Month 5. Our other SaaS services (compliance automation, e-commerce) will provide additional revenue streams for growth and diversification.</p>
                </div>
            </div>
            
            <div class="revenue-cards">
                <div class="revenue-card">
                    <h3>Individual Subscriptions</h3>
                    <p>R200/month per student or tutor. Our core revenue stream targeting private users who want supplemental learning support.</p>
                    <p><strong>Projection:</strong> 60% of total revenue by Year 2</p>
                </div>
                
                <div class="revenue-card">
                    <h3>Enterprise Licenses</h3>
                    <p>R5,000/month per <span class="dynamic-content" data-field="clientType">organization</span> (50+ users). Bulk pricing for <span class="dynamic-content" data-field="clientType">institutions</span> with admin dashboards and analytics.</p>
                    <p><strong>Projection:</strong> 30 <span class="dynamic-content" data-field="clientType">clients</span> by Year 1 (R150,000/month)</p>
                </div>
                
                <div class="revenue-card">
                    <h3>Premium Features</h3>
                    <p>Add-ons like advanced analytics (R50/user/month) and certification (R100/test). High-margin upsell opportunities.</p>
                    <p><strong>Projection:</strong> 15% adoption rate by Year 2</p>
                </div>
            </div>
            
            <div class="chart-container">
                <h3>Revenue Projections</h3>
                <p>Select a growth scenario to visualize our financial trajectory</p>
                
                <div class="chart-controls">
                    <button class="active" data-scenario="optimistic">Optimistic (500 users/month)</button>
                    <button data-scenario="realistic">Realistic (200 users/month)</button>
                    <button data-scenario="conservative">Conservative (100 users/month)</button>
                </div>
                
                <canvas id="revenueChart"></canvas>
            </div>
            
            <div class="revenue-calculator">
                <h3>Custom Projection Calculator</h3>
                <p>Adjust parameters to see how different growth rates affect our breakeven timeline</p>
                
                <div class="calculator-form">
                    <div class="form-group">
                        <label for="userGrowth">New Users Per Month</label>
                        <input type="number" id="userGrowth" value="200" min="50" max="1000">
                    </div>
                    
                    <div class="form-group">
                        <label for="schoolGrowth">New Schools Per Quarter</label>
                        <input type="number" id="schoolGrowth" value="5" min="0" max="20">
                    </div>
                    
                    <div class="form-group">
                        <label for="churnRate">Monthly Churn Rate (%)</label>
                        <input type="number" id="churnRate" value="5" min="0" max="20">
                    </div>
                    
                    <div class="form-group">
                        <label for="premiumUpsell">Premium Upsell Rate (%)</label>
                        <input type="number" id="premiumUpsell" value="10" min="0" max="30">
                    </div>
                </div>
                
                <button class="btn" id="calculateBtn">Calculate Projections</button>
                
                <div class="calculator-results" id="calculatorResults" style="display: none;">
                    <h4>Projection Results</h4>
                    <div class="result-item">
                        <span class="label">Breakeven Month:</span>
                        <span class="value" id="breakevenMonth">Month 8</span>
                    </div>
                    <div class="result-item">
                        <span class="label">Year 1 Revenue:</span>
                        <span class="value" id="year1Revenue">R2,400,000</span>
                    </div>
                    <div class="result-item">
                        <span class="label">Year 2 Revenue:</span>
                        <span class="value" id="year2Revenue">R6,720,000</span>
                    </div>
                    <div class="result-item">
                        <span class="label">Active Users at Breakeven:</span>
                        <span class="value" id="breakevenUsers">1,600</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Competitor Analysis Section -->
    <section class="section competitor-analysis" id="competitors">
        <div class="section-title">
            <h2>Competitor <span>Analysis</span></h2>
            <p>Understanding our competitive landscape and positioning</p>
        </div>

        <div class="competitor-overview">
            <h3>Market Landscape Overview</h3>
            <p>The <span class="dynamic-content" data-field="marketSize">R2.8 billion</span> South African market is growing at 15% annually. Our analysis focuses on direct competitors offering solutions for <span class="dynamic-content" data-field="targetSegment">our target segment</span> and indirect competitors providing related technology solutions.</p>

            <div class="key-competitors">
                <h4>Key Competitors</h4>
                <ul id="competitors-list">
                    <li>Mindset Learn</li>
                    <li>GetSmarter</li>
                    <li>Coursera</li>
                </ul>
            </div>
        </div>

        <div class="competitors-grid">
            <!-- Competitor 1: Mindset Learn -->
            <div class="competitor-card">
                <div class="competitor-header">
                    <h4>Mindset Learn</h4>
                    <span class="competitor-location">Cape Town, South Africa</span>
                    <span class="competitor-status increasing">Market Share: 25% | Status: Increasing</span>
                </div>
                <div class="competitor-content">
                    <div class="competitor-section">
                        <h5>Business Model & Operations</h5>
                        <p><strong>Similar:</strong> CAPS-aligned content, video-based learning, subscription model (R150/month)</p>
                        <p><strong>Different:</strong> Focus on video content vs. our AI-driven interactive approach</p>
                    </div>
                    <div class="competitor-section">
                        <h5>Strengths</h5>
                        <ul>
                            <li>Established brand (15+ years in market)</li>
                            <li>Strong partnerships with DSTV and MultiChoice</li>
                            <li>Extensive video content library</li>
                            <li>Government endorsements and school partnerships</li>
                        </ul>
                    </div>
                    <div class="competitor-section">
                        <h5>Weaknesses</h5>
                        <ul>
                            <li>Limited personalization and AI integration</li>
                            <li>Passive learning experience (video-only)</li>
                            <li>Outdated user interface and mobile experience</li>
                            <li>No real-time tutoring or chatbot assistance</li>
                        </ul>
                    </div>
                    <div class="competitor-section">
                        <h5>Market Reaction & Proximity</h5>
                        <p><strong>Expected Reaction:</strong> Likely to enhance their platform with AI features and improve mobile experience</p>
                        <p><strong>Proximity:</strong> Direct competitor - 500km away, targeting same market segments</p>
                    </div>
                </div>
            </div>

            <!-- Competitor 2: Snapplify -->
            <div class="competitor-card">
                <div class="competitor-header">
                    <h4>Snapplify</h4>
                    <span class="competitor-location">Cape Town, South Africa</span>
                    <span class="competitor-status steady">Market Share: 15% | Status: Steady</span>
                </div>
                <div class="competitor-content">
                    <div class="competitor-section">
                        <h5>Business Model & Operations</h5>
                        <p><strong>Similar:</strong> Digital textbooks, school partnerships, B2B focus</p>
                        <p><strong>Different:</strong> Textbook distribution vs. our interactive learning platform</p>
                    </div>
                    <div class="competitor-section">
                        <h5>Strengths</h5>
                        <ul>
                            <li>Strong publisher relationships and content licensing</li>
                            <li>Established school distribution network</li>
                            <li>International presence (expanding to other African countries)</li>
                            <li>Proven B2B sales model</li>
                        </ul>
                    </div>
                    <div class="competitor-section">
                        <h5>Weaknesses</h5>
                        <ul>
                            <li>Limited interactive features and engagement</li>
                            <li>Focus on content delivery rather than learning outcomes</li>
                            <li>High dependency on traditional publishers</li>
                            <li>Limited analytics and progress tracking</li>
                        </ul>
                    </div>
                    <div class="competitor-section">
                        <h5>Market Reaction & Proximity</h5>
                        <p><strong>Expected Reaction:</strong> May develop interactive features or acquire EdTech startups</p>
                        <p><strong>Proximity:</strong> Indirect competitor - 500km away, different primary focus</p>
                    </div>
                </div>
            </div>

            <!-- Competitor 3: Siyavula -->
            <div class="competitor-card">
                <div class="competitor-header">
                    <h4>Siyavula</h4>
                    <span class="competitor-location">Cape Town, South Africa</span>
                    <span class="competitor-status increasing">Market Share: 20% | Status: Increasing</span>
                </div>
                <div class="competitor-content">
                    <div class="competitor-section">
                        <h5>Business Model & Operations</h5>
                        <p><strong>Similar:</strong> CAPS-aligned, mathematics and science focus, adaptive learning</p>
                        <p><strong>Different:</strong> Subject-specific vs. our comprehensive curriculum approach</p>
                    </div>
                    <div class="competitor-section">
                        <h5>Strengths</h5>
                        <ul>
                            <li>Strong adaptive learning algorithms for math and science</li>
                            <li>Proven track record with measurable learning outcomes</li>
                            <li>Open-source textbook development</li>
                            <li>Strong research partnerships with universities</li>
                        </ul>
                    </div>
                    <div class="competitor-section">
                        <h5>Weaknesses</h5>
                        <ul>
                            <li>Limited to mathematics and science subjects</li>
                            <li>Complex pricing structure</li>
                            <li>Limited marketing and brand awareness</li>
                            <li>Narrow target market (STEM subjects only)</li>
                        </ul>
                    </div>
                    <div class="competitor-section">
                        <h5>Market Reaction & Proximity</h5>
                        <p><strong>Expected Reaction:</strong> May expand to other subjects or enhance AI capabilities</p>
                        <p><strong>Proximity:</strong> Direct competitor - 500km away, overlapping in STEM education</p>
                    </div>
                </div>
            </div>

            <!-- Competitor 4: Global Players -->
            <div class="competitor-card">
                <div class="competitor-header">
                    <h4>Global EdTech Giants</h4>
                    <span class="competitor-location">International (Google, Microsoft, Khan Academy)</span>
                    <span class="competitor-status increasing">Market Share: 30% | Status: Increasing</span>
                </div>
                <div class="competitor-content">
                    <div class="competitor-section">
                        <h5>Business Model & Operations</h5>
                        <p><strong>Similar:</strong> AI-powered learning, global reach, technology integration</p>
                        <p><strong>Different:</strong> Generic global content vs. our localized CAPS/IEB alignment</p>
                    </div>
                    <div class="competitor-section">
                        <h5>Strengths</h5>
                        <ul>
                            <li>Massive resources and R&D capabilities</li>
                            <li>Advanced AI and machine learning technologies</li>
                            <li>Global brand recognition and trust</li>
                            <li>Integration with existing school technology infrastructure</li>
                        </ul>
                    </div>
                    <div class="competitor-section">
                        <h5>Weaknesses</h5>
                        <ul>
                            <li>Lack of local curriculum alignment (CAPS/IEB)</li>
                            <li>Limited understanding of South African education context</li>
                            <li>Generic content not tailored to local needs</li>
                            <li>Complex implementation and high costs for schools</li>
                        </ul>
                    </div>
                    <div class="competitor-section">
                        <h5>Market Reaction & Proximity</h5>
                        <p><strong>Expected Reaction:</strong> May partner with local content providers or acquire SA EdTech companies</p>
                        <p><strong>Proximity:</strong> Indirect competitor - Global presence, different market approach</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="competitive-advantage">
            <h3>Our Competitive Advantage</h3>
            <div class="advantage-grid">
                <div class="advantage-card">
                    <h4>Sustainable Competitive Advantages</h4>
                    <ul>
                        <li><strong>Deep Local Expertise:</strong> CAPS/IEB curriculum alignment with native language support</li>
                        <li><strong>AI-Powered Personalization:</strong> Advanced chatbot tutoring in local context</li>
                        <li><strong>Cost-Effective Solution:</strong> R200/month vs competitors' R300-500/month</li>
                        <li><strong>Mobile-First Design:</strong> Optimized for South African smartphone usage patterns</li>
                        <li><strong>Community Integration:</strong> Built-in peer learning and teacher collaboration tools</li>
                    </ul>
                </div>
                <div class="advantage-card">
                    <h4>How We Will Beat Competitors</h4>
                    <ul>
                        <li><strong>Speed to Market:</strong> Faster implementation and onboarding (2 weeks vs 3 months)</li>
                        <li><strong>Local Support:</strong> Durban-based team with same-day support</li>
                        <li><strong>Flexible Pricing:</strong> Multiple payment options including mobile money</li>
                        <li><strong>Continuous Innovation:</strong> Monthly feature updates based on user feedback</li>
                        <li><strong>Partnership Strategy:</strong> Direct relationships with teacher unions and education departments</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="competitive-comparison">
            <h3>Competitive Positioning Matrix</h3>
            <div class="comparison-table">
                <table>
                    <thead>
                        <tr>
                            <th>Factor</th>
                            <th>Solving Tomorrow</th>
                            <th>Mindset Learn</th>
                            <th>Siyavula</th>
                            <th>Global Players</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>CAPS/IEB Alignment</td>
                            <td class="rating strong">Strong</td>
                            <td class="rating strong">Strong</td>
                            <td class="rating strong">Strong</td>
                            <td class="rating poor">Poor</td>
                        </tr>
                        <tr>
                            <td>AI Integration</td>
                            <td class="rating strong">Strong</td>
                            <td class="rating poor">Poor</td>
                            <td class="rating fair">Fair</td>
                            <td class="rating strong">Strong</td>
                        </tr>
                        <tr>
                            <td>Local Market Understanding</td>
                            <td class="rating strong">Strong</td>
                            <td class="rating strong">Strong</td>
                            <td class="rating strong">Strong</td>
                            <td class="rating poor">Poor</td>
                        </tr>
                        <tr>
                            <td>Technology Innovation</td>
                            <td class="rating strong">Strong</td>
                            <td class="rating fair">Fair</td>
                            <td class="rating strong">Strong</td>
                            <td class="rating strong">Strong</td>
                        </tr>
                        <tr>
                            <td>Pricing Competitiveness</td>
                            <td class="rating strong">Strong</td>
                            <td class="rating fair">Fair</td>
                            <td class="rating poor">Poor</td>
                            <td class="rating poor">Poor</td>
                        </tr>
                        <tr>
                            <td>Brand Recognition</td>
                            <td class="rating poor">Poor</td>
                            <td class="rating strong">Strong</td>
                            <td class="rating fair">Fair</td>
                            <td class="rating strong">Strong</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </section>

    <!-- Enhanced SWOT Analysis Section -->
    <section class="section" id="swot">
        <div class="section-title">
            <h2>SWOT <span>Analysis</span></h2>
            <p>Comprehensive analysis of our strategic position</p>
        </div>

        <div class="swot-intro">
            <p>Our SWOT analysis reveals a strong foundation for market entry with clear competitive advantages in <span class="dynamic-content" data-field="valueProposition">local expertise and AI integration</span>. While we face challenges as a new entrant, significant opportunities exist in the growing market for <span class="dynamic-content" data-field="targetSegment">our target segment</span>.</p>
        </div>

        <div class="swot-grid">
            <div class="swot-card strength">
                <h3>5.1 Strengths</h3>
                <div class="swot-content">
                    <h4>Sustainable Competitive Advantages</h4>
                    <ul>
                        <li><strong>Deep Industry Expertise:</strong> Specialized knowledge in <span class="dynamic-content" data-field="targetSegment">target market</span> with local language support</li>
                        <li><strong>Advanced AI Integration:</strong> Proprietary technology with contextual intelligence and 24/7 support capabilities</li>
                        <li><strong>Experienced Team:</strong> Combined 50+ years in technology, AI, and South African business</li>
                        <li><strong>Cost Leadership:</strong> 40% lower pricing than established competitors while offering superior features</li>
                        <li><strong>Mobile-First Architecture:</strong> Optimized for South African smartphone usage patterns and data constraints</li>
                        <li><strong>Local Market Insight:</strong> Deep understanding of South African business challenges and cultural context</li>
                        <li><strong>Agile Development:</strong> Rapid iteration and feature deployment based on user feedback</li>
                        <li><strong>Strong Legal Foundation:</strong> POPIA compliance and BEE certification from day one</li>
                        <li><strong>Bootstrap Advantage:</strong> 100% self-funded with complete ownership and control</li>
                        <li><strong>Lean Operations:</strong> Proven ability to achieve more with less, forced efficiency</li>
                    </ul>

                    <h4>Source of Competitive Advantage</h4>
                    <p>Our competitive advantage stems from the unique combination of local education expertise, advanced AI technology, and deep understanding of South African market needs. This creates a sustainable moat that global competitors cannot easily replicate.</p>
                </div>
            </div>

            <div class="swot-card weakness">
                <h3>5.2 Weaknesses</h3>
                <div class="swot-content">
                    <h4>Areas Requiring Development</h4>
                    <ul>
                        <li><strong>Brand Recognition:</strong> New market entrant with limited brand awareness compared to established players</li>
                        <li><strong>Bootstrap Constraints:</strong> R80,000 capital requires disciplined spending and organic growth</li>
                        <li><strong>Customer Acquisition:</strong> Dependency on achieving critical mass of 1,500 users for breakeven</li>
                        <li><strong>Feature Library:</strong> Smaller initial feature base compared to competitors with 15+ years of development</li>
                        <li><strong>Sales Infrastructure:</strong> Limited sales team and distribution channels compared to established competitors</li>
                        <li><strong>Technology Risk:</strong> Heavy reliance on AI technology that requires continuous improvement and maintenance</li>
                        <li><strong>Market Validation:</strong> Limited proven track record with only 3 pilot schools currently</li>
                    </ul>

                    <h4>Business Vulnerabilities</h4>
                    <p>Our main vulnerabilities lie in market penetration speed and the need to prove our value proposition quickly to achieve sustainable growth before funding runs out.</p>
                </div>
            </div>

            <div class="swot-card opportunity">
                <h3>5.3 Opportunities</h3>
                <div class="swot-content">
                    <h4>Market Opportunities</h4>
                    <ul id="opportunities-list">
                        <li><strong>Post-COVID Education Shift:</strong> 78% of SA schools now embrace digital learning solutions</li>
                        <li><strong>Government EdTech Investment:</strong> R2.5 billion allocated for education technology in 2025-2027</li>
                        <li><strong>Mobile Penetration Growth:</strong> 95% smartphone penetration among target demographic (ages 13-18)</li>
                        <li><strong>Teacher Shortage Crisis:</strong> 30,000 teacher shortage creates demand for AI tutoring solutions</li>
                        <li><strong>International Expansion:</strong> Potential to expand to other African countries with similar curricula</li>
                        <li><strong>Corporate Partnerships:</strong> Opportunities with telecommunications companies for data-free access</li>
                        <li><strong>NGO Collaborations:</strong> Partnerships with education-focused NGOs for underserved communities</li>
                        <li><strong>Assessment Integration:</strong> Potential partnerships with examination boards for official assessment tools</li>
                    </ul>

                    <h4>Industry Shape & Market Readiness</h4>
                    <p>The South African EdTech market is experiencing unprecedented growth (15% annually) with strong government support and increasing acceptance of digital learning solutions. The market is ready for innovative, locally-relevant solutions.</p>
                </div>
            </div>

            <div class="swot-card threat">
                <h3>5.4 Threats</h3>
                <div class="swot-content">
                    <h4>External Challenges</h4>
                    <ul id="threats-list">
                        <li><strong>Global Tech Giants:</strong> Google, Microsoft may develop localized solutions or acquire local competitors</li>
                        <li><strong>Economic Constraints:</strong> School budget cuts and economic pressure on families may reduce spending</li>
                        <li><strong>Infrastructure Limitations:</strong> Unreliable internet and electricity in rural areas limits market reach</li>
                        <li><strong>Regulatory Changes:</strong> Potential changes in education policy or data protection laws</li>
                        <li><strong>Competitive Response:</strong> Established players may lower prices or enhance features to maintain market share</li>
                        <li><strong>Technology Disruption:</strong> Rapid AI advancement may make our current technology obsolete</li>
                        <li><strong>Funding Challenges:</strong> Difficulty securing additional funding in competitive startup environment</li>
                        <li><strong>Talent Acquisition:</strong> Competition for skilled AI and EdTech professionals</li>
                    </ul>

                    <h4>Customer Demand Validation</h4>
                    <p>Strong customer demand exists with 85% of surveyed teachers expressing interest in AI tutoring tools. However, price sensitivity and slow adoption in under-resourced schools remain concerns.</p>
                </div>
            </div>
        </div>

        <div class="swot-actions">
            <h3>5.5 SWOT - Strategic Actions</h3>
            <div class="actions-grid">
                <div class="action-card weakness-action">
                    <h4>Addressing Weaknesses</h4>
                    <div class="action-item">
                        <h5>Brand Recognition → Strength</h5>
                        <p><strong>Strategy:</strong> Implement aggressive content marketing and thought leadership campaign</p>
                        <ul>
                            <li>Partner with education influencers and teacher unions</li>
                            <li>Publish research on AI in South African education</li>
                            <li>Sponsor education conferences and teacher development programs</li>
                            <li>Leverage pilot school success stories for case studies</li>
                        </ul>
                    </div>
                    <div class="action-item">
                        <h5>Bootstrap Constraints → Lean Efficiency</h5>
                        <p><strong>Strategy:</strong> Maximize ROI through disciplined spending and organic growth</p>
                        <ul>
                            <li>Prioritize word-of-mouth and referral programs (zero cost)</li>
                            <li>Partner with teacher training institutions for mutual benefit</li>
                            <li>Implement freemium model to reduce acquisition costs</li>
                            <li>Focus on revenue-generating activities first</li>
                            <li>Reinvest all profits strategically for sustainable growth</li>
                        </ul>
                    </div>
                    <div class="action-item">
                        <h5>Content Library → Rapid Expansion</h5>
                        <p><strong>Strategy:</strong> Leverage AI and community to accelerate content creation</p>
                        <ul>
                            <li>Use AI to generate practice questions and explanations</li>
                            <li>Crowdsource content from teacher community</li>
                            <li>Partner with existing content creators and publishers</li>
                            <li>Focus on high-impact subjects first (Mathematics, Science, English)</li>
                        </ul>
                    </div>
                </div>

                <div class="action-card threat-action">
                    <h4>Mitigating Threats</h4>
                    <div class="action-item">
                        <h5>Global Competition → Local Advantage</h5>
                        <p><strong>Strategy:</strong> Strengthen local market position and create switching costs</p>
                        <ul>
                            <li>Deepen curriculum integration and local language support</li>
                            <li>Build strong relationships with education departments</li>
                            <li>Create network effects through peer learning features</li>
                            <li>Establish exclusive partnerships with key stakeholders</li>
                        </ul>
                    </div>
                    <div class="action-item">
                        <h5>Economic Constraints → Value Demonstration</h5>
                        <p><strong>Strategy:</strong> Prove clear ROI and offer flexible payment options</p>
                        <ul>
                            <li>Conduct and publish studies on learning outcome improvements</li>
                            <li>Offer pay-per-performance pricing models</li>
                            <li>Partner with NGOs for subsidized access</li>
                            <li>Implement mobile money and installment payment options</li>
                        </ul>
                    </div>
                    <div class="action-item">
                        <h5>Infrastructure Limitations → Offline Solutions</h5>
                        <p><strong>Strategy:</strong> Develop offline-capable features and data-light solutions</p>
                        <ul>
                            <li>Create offline mode with content synchronization</li>
                            <li>Partner with telecommunications for zero-rated data</li>
                            <li>Optimize for low-bandwidth environments</li>
                            <li>Develop SMS-based learning modules for basic phones</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Market Analysis Section -->
    <section class="section market-analysis" id="market">
        <div class="section-title">
            <h2>Market <span>Analysis</span></h2>
            <p>Understanding the South African EdTech landscape</p>
        </div>

        <div class="market-content">
            <div class="market-overview">
                <h3>Industry Characteristics & Market Shape</h3>
                <div class="market-stats">
                    <div class="stat-card">
                        <h4>Market Size</h4>
                        <span class="stat-value">R2.8 Billion</span>
                        <p>South African EdTech market value in 2025</p>
                    </div>
                    <div class="stat-card">
                        <h4>Growth Rate</h4>
                        <span class="stat-value">15% CAGR</span>
                        <p>Annual compound growth rate (2023-2028)</p>
                    </div>
                    <div class="stat-card">
                        <h4>Digital Adoption</h4>
                        <span class="stat-value">78%</span>
                        <p>Schools embracing digital learning post-COVID</p>
                    </div>
                    <div class="stat-card">
                        <h4>Target Market</h4>
                        <span class="stat-value">12.3M</span>
                        <p>Students in South African schools (Grades 1-12)</p>
                    </div>
                </div>

                <div class="market-drivers">
                    <h4>Key Market Drivers</h4>
                    <div class="drivers-grid">
                        <div class="driver-item">
                            <i class="fas fa-virus"></i>
                            <h5>Post-COVID Digital Transformation</h5>
                            <p>Accelerated adoption of digital learning tools and remote education capabilities</p>
                        </div>
                        <div class="driver-item">
                            <i class="fas fa-mobile-alt"></i>
                            <h5>Mobile Technology Penetration</h5>
                            <p>95% smartphone penetration among target demographic (ages 13-18)</p>
                        </div>
                        <div class="driver-item">
                            <i class="fas fa-graduation-cap"></i>
                            <h5>Teacher Shortage Crisis</h5>
                            <p>30,000 teacher shortage creates demand for AI-assisted learning solutions</p>
                        </div>
                        <div class="driver-item">
                            <i class="fas fa-coins"></i>
                            <h5>Government Investment</h5>
                            <p>R2.5 billion allocated for education technology initiatives (2025-2027)</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="customer-demand">
                <h3>Customer Demand Validation</h3>
                <div class="demand-analysis">
                    <div class="demand-section">
                        <h4>Primary Research Findings</h4>
                        <div class="research-stats">
                            <div class="research-item">
                                <span class="percentage">85%</span>
                                <p>of surveyed teachers express interest in AI tutoring tools</p>
                            </div>
                            <div class="research-item">
                                <span class="percentage">72%</span>
                                <p>of parents willing to pay for supplemental learning support</p>
                            </div>
                            <div class="research-item">
                                <span class="percentage">68%</span>
                                <p>of students prefer interactive learning over traditional methods</p>
                            </div>
                            <div class="research-item">
                                <span class="percentage">91%</span>
                                <p>of school principals see value in curriculum-aligned digital tools</p>
                            </div>
                        </div>
                    </div>

                    <div class="demand-section">
                        <h4>Market Validation Evidence</h4>
                        <ul class="validation-list">
                            <li><strong>Pilot Program Success:</strong> 3 schools showing 23% improvement in test scores</li>
                            <li><strong>Waitlist Growth:</strong> 450+ families on pre-launch waitlist</li>
                            <li><strong>Teacher Feedback:</strong> 94% satisfaction rate in pilot programs</li>
                            <li><strong>Competitive Analysis:</strong> Existing solutions have 6-month waiting lists</li>
                            <li><strong>Government Support:</strong> Department of Education endorsement for pilot expansion</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="target-segments">
                <h3>Target Market Segmentation</h3>
                <div class="segments-grid">
                    <div class="segment-card primary">
                        <h4>Primary Segment: Individual Students & Tutors</h4>
                        <div class="segment-details">
                            <p><strong>Size:</strong> 2.1M students (Grades 8-12)</p>
                            <p><strong>Revenue Potential:</strong> R200/month × 50,000 users = R10M/month</p>
                            <p><strong>Characteristics:</strong></p>
                            <ul>
                                <li>Ages 13-18, smartphone access, academic pressure</li>
                                <li>Parents earning R15,000+ household income</li>
                                <li>Urban and semi-urban areas with reliable internet</li>
                                <li>Preparing for NSC examinations</li>
                            </ul>
                        </div>
                    </div>

                    <div class="segment-card secondary">
                        <h4>Secondary Segment: Schools & Institutions</h4>
                        <div class="segment-details">
                            <p><strong>Size:</strong> 1,200 high schools (Grades 8-12)</p>
                            <p><strong>Revenue Potential:</strong> R5,000/month × 300 schools = R1.5M/month</p>
                            <p><strong>Characteristics:</strong></p>
                            <ul>
                                <li>Private and Model C schools with technology budgets</li>
                                <li>Progressive principals seeking competitive advantages</li>
                                <li>Schools with 200+ students in senior grades</li>
                                <li>Located in major metropolitan areas</li>
                            </ul>
                        </div>
                    </div>

                    <div class="segment-card tertiary">
                        <h4>Tertiary Segment: Tutoring Centers & NGOs</h4>
                        <div class="segment-details">
                            <p><strong>Size:</strong> 800 tutoring centers nationwide</p>
                            <p><strong>Revenue Potential:</strong> R3,000/month × 200 centers = R600K/month</p>
                            <p><strong>Characteristics:</strong></p>
                            <ul>
                                <li>Private tutoring businesses seeking efficiency</li>
                                <li>NGOs focused on education in underserved communities</li>
                                <li>After-school programs and community centers</li>
                                <li>Organizations with education-focused mandates</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Marketing Plan Section -->
    <section class="section marketing" id="marketing">
        <div class="section-title">
            <h2>Marketing <span>Plan</span></h2>
            <p>Comprehensive strategy to capture market share</p>
        </div>
        <div class="marketing-content">
            <div class="marketing-objectives">
                <h3>Strategic Marketing Objectives</h3>
                <div class="objectives-timeline">
                    <div class="objective-item">
                        <h4>Q4 2025 - Market Entry</h4>
                        <ul>
                            <li>Reach 1,500 users by Month 5 (<span class="dynamic-content" data-field="productName">Lumerous</span> breakeven)</li>
                            <li>Establish presence in 10 pilot <span class="dynamic-content" data-field="clientType">organizations</span></li>
                            <li>Achieve 15% brand awareness in target market</li>
                            <li>Generate 1,000 qualified leads</li>
                            <li>Begin profit allocation strategy implementation</li>
                        </ul>
                    </div>
                    <div class="objective-item">
                        <h4>Q2 2026 - Growth Phase</h4>
                        <ul>
                            <li>Scale to 1,500 active users</li>
                            <li>Expand to 30 school partnerships</li>
                            <li>Achieve 35% brand awareness</li>
                            <li>Launch referral program with 25% participation</li>
                        </ul>
                    </div>
                    <div class="objective-item">
                        <h4>Q4 2026 - Market Leadership</h4>
                        <ul>
                            <li>Reach 5,000 active users</li>
                            <li>Establish presence in 100 schools</li>
                            <li>Achieve 50% brand awareness in target segments</li>
                            <li>Launch in 2 additional provinces</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="marketing-strategy">
                <h3>Multi-Channel Marketing Strategy</h3>
                <div class="strategy-channels">
                    <div class="channel-card">
                        <h4>Digital Marketing (40% of budget)</h4>
                        <ul>
                            <li><strong>Social Media:</strong> Facebook, Instagram, TikTok targeting parents and students</li>
                            <li><strong>LinkedIn:</strong> B2B outreach to school principals and education officials</li>
                            <li><strong>Google Ads:</strong> Search campaigns for education-related keywords</li>
                            <li><strong>Content Marketing:</strong> Educational blog, YouTube tutorials, webinars</li>
                            <li><strong>Email Campaigns:</strong> Nurture sequences for leads and customer retention</li>
                        </ul>
                    </div>

                    <div class="channel-card">
                        <h4>Direct Sales & Partnerships (35% of budget)</h4>
                        <ul>
                            <li><strong>School Outreach:</strong> Direct presentations to principals and governing bodies</li>
                            <li><strong>Education Conferences:</strong> Presence at SAOU, NAPTOSA, and provincial events</li>
                            <li><strong>Teacher Union Partnerships:</strong> Collaborative programs with SADTU and NATU</li>
                            <li><strong>Government Relations:</strong> Engagement with provincial education departments</li>
                            <li><strong>Pilot Programs:</strong> Free trials for influential schools and districts</li>
                        </ul>
                    </div>

                    <div class="channel-card">
                        <h4>Community & Referral Marketing (25% of budget)</h4>
                        <ul>
                            <li><strong>Referral Program:</strong> R50 credit for successful student referrals</li>
                            <li><strong>Influencer Partnerships:</strong> Education bloggers and teacher influencers</li>
                            <li><strong>Community Events:</strong> Parent-teacher meetings and education fairs</li>
                            <li><strong>Word-of-Mouth:</strong> Exceptional customer service and success stories</li>
                            <li><strong>Student Ambassadors:</strong> Peer-to-peer marketing in schools</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="marketing-budget">
                <h3>Annual Marketing Budget: R600,000</h3>
                <div class="budget-breakdown">
                    <div class="budget-category">
                        <h4>Digital Marketing - R240,000 (40%)</h4>
                        <div class="budget-items">
                            <div class="budget-item">
                                <span class="amount">R120,000</span>
                                <span class="category">Social Media Advertising (Facebook, Instagram, TikTok)</span>
                            </div>
                            <div class="budget-item">
                                <span class="amount">R60,000</span>
                                <span class="category">Google Ads & Search Marketing</span>
                            </div>
                            <div class="budget-item">
                                <span class="amount">R36,000</span>
                                <span class="category">Content Creation & Video Production</span>
                            </div>
                            <div class="budget-item">
                                <span class="amount">R24,000</span>
                                <span class="category">Email Marketing & Marketing Automation</span>
                            </div>
                        </div>
                    </div>

                    <div class="budget-category">
                        <h4>Direct Sales & Events - R210,000 (35%)</h4>
                        <div class="budget-items">
                            <div class="budget-item">
                                <span class="amount">R84,000</span>
                                <span class="category">Education Conferences & Trade Shows</span>
                            </div>
                            <div class="budget-item">
                                <span class="amount">R60,000</span>
                                <span class="category">Sales Team Travel & Presentations</span>
                            </div>
                            <div class="budget-item">
                                <span class="amount">R42,000</span>
                                <span class="category">Printed Materials & Brochures</span>
                            </div>
                            <div class="budget-item">
                                <span class="amount">R24,000</span>
                                <span class="category">Pilot Program Incentives</span>
                            </div>
                        </div>
                    </div>

                    <div class="budget-category">
                        <h4>Community & Referrals - R150,000 (25%)</h4>
                        <div class="budget-items">
                            <div class="budget-item">
                                <span class="amount">R72,000</span>
                                <span class="category">Referral Program Rewards & Incentives</span>
                            </div>
                            <div class="budget-item">
                                <span class="amount">R48,000</span>
                                <span class="category">Influencer Partnerships & Sponsorships</span>
                            </div>
                            <div class="budget-item">
                                <span class="amount">R30,000</span>
                                <span class="category">Community Events & Workshops</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="distribution-strategy">
                <h3>Distribution & Channel Strategy</h3>
                <div class="distribution-channels">
                    <div class="channel-strategy">
                        <h4>Direct-to-Consumer (B2C)</h4>
                        <ul>
                            <li><strong>Online Platform:</strong> Self-service registration and onboarding</li>
                            <li><strong>Mobile App:</strong> iOS and Android apps for easy access</li>
                            <li><strong>Social Media:</strong> Direct engagement and customer acquisition</li>
                            <li><strong>Referral Network:</strong> Existing users driving new acquisitions</li>
                        </ul>
                    </div>

                    <div class="channel-strategy">
                        <h4>Business-to-Business (B2B)</h4>
                        <ul>
                            <li><strong>Direct Sales:</strong> Dedicated sales team for school outreach</li>
                            <li><strong>Education Consultants:</strong> Third-party consultants selling our solution</li>
                            <li><strong>Technology Partners:</strong> Integration with existing school management systems</li>
                            <li><strong>Government Channels:</strong> Provincial education department partnerships</li>
                        </ul>
                    </div>

                    <div class="channel-strategy">
                        <h4>Strategic Partnerships</h4>
                        <ul>
                            <li><strong>Telecommunications:</strong> MTN, Vodacom for zero-rated data access</li>
                            <li><strong>Publishers:</strong> Content partnerships with local educational publishers</li>
                            <li><strong>NGOs:</strong> Collaborations for underserved community access</li>
                            <li><strong>Teacher Training:</strong> Integration with teacher development programs</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

<!-- Risk Analysis Section -->
<section class="section risk-analysis" id="risk">
    <div class="section-title">
        <h2>Risk <span>Analysis</span></h2>
        <p>Comprehensive risk assessment and mitigation strategies</p>
    </div>

    <div class="risk-content">
        <div class="risk-overview">
            <h3>Risk Management Framework</h3>
            <p>Our risk management approach follows a comprehensive framework that identifies, assesses, and mitigates potential threats to our business operations and growth objectives. We categorize risks into four main areas: Market, Technology, Financial, and Operational risks.</p>
        </div>

        <div class="risk-matrix">
            <h3>Risk Assessment Matrix</h3>
            <div class="risks-grid">
                <!-- High Impact, High Probability -->
                <div class="risk-card high-high">
                    <h4>High Impact, High Probability</h4>
                    <div class="risk-item">
                        <h5>Competitive Response from Established Players</h5>
                        <p><strong>Risk:</strong> Mindset Learn or Siyavula rapidly develops AI features to compete directly</p>
                        <p><strong>Impact:</strong> Loss of competitive advantage, reduced market share</p>
                        <p><strong>Mitigation:</strong> Continuous innovation, patent applications, exclusive partnerships</p>
                    </div>
                    <div class="risk-item">
                        <h5>Slow Customer Acquisition</h5>
                        <p><strong>Risk:</strong> Difficulty reaching 1,500 users needed for breakeven</p>
                        <p><strong>Impact:</strong> Cash flow problems, potential business failure</p>
                        <p><strong>Mitigation:</strong> Aggressive marketing, freemium model, referral incentives</p>
                    </div>
                </div>

                <!-- High Impact, Low Probability -->
                <div class="risk-card high-low">
                    <h4>High Impact, Low Probability</h4>
                    <div class="risk-item">
                        <h5>Global Tech Giant Market Entry</h5>
                        <p><strong>Risk:</strong> Google or Microsoft launches localized SA education platform</p>
                        <p><strong>Impact:</strong> Massive competitive disadvantage, market disruption</p>
                        <p><strong>Mitigation:</strong> Strong local partnerships, government relationships, niche focus</p>
                    </div>
                    <div class="risk-item">
                        <h5>Major Economic Recession</h5>
                        <p><strong>Risk:</strong> Economic downturn reduces education spending significantly</p>
                        <p><strong>Impact:</strong> Reduced customer base, pricing pressure</p>
                        <p><strong>Mitigation:</strong> Flexible pricing, NGO partnerships, government contracts</p>
                    </div>
                </div>

                <!-- Low Impact, High Probability -->
                <div class="risk-card low-high">
                    <h4>Low Impact, High Probability</h4>
                    <div class="risk-item">
                        <h5>Technology Infrastructure Issues</h5>
                        <p><strong>Risk:</strong> Server downtime, connectivity issues in rural areas</p>
                        <p><strong>Impact:</strong> Customer dissatisfaction, churn</p>
                        <p><strong>Mitigation:</strong> Redundant systems, offline capabilities, multiple data centers</p>
                    </div>
                    <div class="risk-item">
                        <h5>Staff Turnover</h5>
                        <p><strong>Risk:</strong> Key technical staff leaving for better opportunities</p>
                        <p><strong>Impact:</strong> Development delays, knowledge loss</p>
                        <p><strong>Mitigation:</strong> Competitive compensation, equity participation, strong culture</p>
                    </div>
                </div>

                <!-- Low Impact, Low Probability -->
                <div class="risk-card low-low">
                    <h4>Low Impact, Low Probability</h4>
                    <div class="risk-item">
                        <h5>Regulatory Changes</h5>
                        <p><strong>Risk:</strong> Changes in education policy or data protection laws</p>
                        <p><strong>Impact:</strong> Compliance costs, feature modifications</p>
                        <p><strong>Mitigation:</strong> Legal monitoring, compliance framework, government engagement</p>
                    </div>
                    <div class="risk-item">
                        <h5>Currency Fluctuation</h5>
                        <p><strong>Risk:</strong> Rand volatility affecting international partnerships</p>
                        <p><strong>Impact:</strong> Increased costs for international services</p>
                        <p><strong>Mitigation:</strong> Local suppliers, rand-denominated contracts, hedging strategies</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="contingency-plans">
            <h3>Contingency Plans</h3>
            <div class="contingency-grid">
                <div class="contingency-card">
                    <h4>Financial Contingency</h4>
                    <div class="scenario">
                        <h5>Scenario: Funding Shortfall</h5>
                        <p><strong>Trigger:</strong> Unable to raise additional funding by Month 6</p>
                        <p><strong>Response Plan:</strong></p>
                        <ul>
                            <li>Reduce team size by 30% (non-essential roles)</li>
                            <li>Focus on core features only</li>
                            <li>Seek strategic partnerships for funding</li>
                            <li>Consider acquisition by larger EdTech company</li>
                            <li>Implement emergency cost-cutting measures</li>
                        </ul>
                    </div>
                </div>

                <div class="contingency-card">
                    <h4>Market Contingency</h4>
                    <div class="scenario">
                        <h5>Scenario: Major Competitor Launch</h5>
                        <p><strong>Trigger:</strong> Established player launches similar AI-powered platform</p>
                        <p><strong>Response Plan:</strong></p>
                        <ul>
                            <li>Accelerate unique feature development</li>
                            <li>Strengthen customer relationships and loyalty programs</li>
                            <li>Pivot to underserved market segments</li>
                            <li>Explore partnership or acquisition opportunities</li>
                            <li>Enhance local market advantages</li>
                        </ul>
                    </div>
                </div>

                <div class="contingency-card">
                    <h4>Technology Contingency</h4>
                    <div class="scenario">
                        <h5>Scenario: Critical System Failure</h5>
                        <p><strong>Trigger:</strong> Major platform outage lasting more than 24 hours</p>
                        <p><strong>Response Plan:</strong></p>
                        <ul>
                            <li>Activate backup systems and disaster recovery</li>
                            <li>Implement emergency communication protocol</li>
                            <li>Provide service credits to affected customers</li>
                            <li>Conduct thorough post-incident analysis</li>
                            <li>Strengthen infrastructure and monitoring</li>
                        </ul>
                    </div>
                </div>

                <div class="contingency-card">
                    <h4>Operational Contingency</h4>
                    <div class="scenario">
                        <h5>Scenario: Key Personnel Loss</h5>
                        <p><strong>Trigger:</strong> CTO or lead developer leaves unexpectedly</p>
                        <p><strong>Response Plan:</strong></p>
                        <ul>
                            <li>Activate knowledge transfer protocols</li>
                            <li>Engage backup technical consultants</li>
                            <li>Accelerate hiring process for replacement</li>
                            <li>Redistribute responsibilities among team</li>
                            <li>Consider temporary external technical support</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="risk-monitoring">
            <h3>Risk Monitoring & Review</h3>
            <div class="monitoring-framework">
                <div class="monitoring-section">
                    <h4>Key Risk Indicators (KRIs) & Mitigation Strategies</h4>
                    <div class="kri-detailed-list">
                        <div class="kri-detailed-item">
                            <div class="kri-header">
                                <span class="kri-metric">Customer Acquisition Rate</span>
                                <span class="kri-threshold">< 150 users/month</span>
                            </div>
                            <div class="kri-mitigation">
                                <h5>Immediate Actions (Within 48 hours):</h5>
                                <ol>
                                    <li>Analyze acquisition funnel metrics and identify bottlenecks</li>
                                    <li>Review and optimize marketing spend allocation</li>
                                    <li>Conduct emergency customer interviews to identify barriers</li>
                                    <li>Activate referral program incentives</li>
                                </ol>
                                <h5>Medium-term Actions (1-2 weeks):</h5>
                                <ol>
                                    <li>Launch targeted social media campaigns</li>
                                    <li>Implement freemium trial extensions</li>
                                    <li>Partner with additional schools for pilot programs</li>
                                    <li>Enhance onboarding process based on user feedback</li>
                                </ol>
                            </div>
                        </div>

                        <div class="kri-detailed-item">
                            <div class="kri-header">
                                <span class="kri-metric">Monthly Churn Rate</span>
                                <span class="kri-threshold">> 8%</span>
                            </div>
                            <div class="kri-mitigation">
                                <h5>Immediate Actions (Within 24 hours):</h5>
                                <ol>
                                    <li>Contact churning users for exit interviews</li>
                                    <li>Analyze usage patterns of churned users</li>
                                    <li>Implement retention campaigns for at-risk users</li>
                                    <li>Review and fix critical product issues</li>
                                </ol>
                                <h5>Medium-term Actions (1-2 weeks):</h5>
                                <ol>
                                    <li>Enhance customer success program</li>
                                    <li>Improve product features based on feedback</li>
                                    <li>Implement predictive churn modeling</li>
                                    <li>Launch win-back campaigns for recent churns</li>
                                </ol>
                            </div>
                        </div>

                        <div class="kri-detailed-item">
                            <div class="kri-header">
                                <span class="kri-metric">Cash Runway</span>
                                <span class="kri-threshold">< 6 months</span>
                            </div>
                            <div class="kri-mitigation">
                                <h5>Immediate Actions (Within 24 hours):</h5>
                                <ol>
                                    <li>Activate emergency cost reduction measures</li>
                                    <li>Accelerate revenue collection and sales efforts</li>
                                    <li>Implement immediate cash flow optimization</li>
                                    <li>Consider short-term service contracts for quick revenue</li>
                                </ol>
                                <h5>Medium-term Actions (1-4 weeks):</h5>
                                <ol>
                                    <li>Explore strategic partnerships for revenue sharing</li>
                                    <li>Consider offering additional services for quick income</li>
                                    <li>Implement aggressive customer acquisition campaigns</li>
                                    <li>Evaluate non-essential expense elimination</li>
                                </ol>
                            </div>
                        </div>

                        <div class="kri-detailed-item">
                            <div class="kri-header">
                                <span class="kri-metric">System Uptime</span>
                                <span class="kri-threshold">< 99.5%</span>
                            </div>
                            <div class="kri-mitigation">
                                <h5>Immediate Actions (Within 1 hour):</h5>
                                <ol>
                                    <li>Activate incident response team</li>
                                    <li>Implement backup systems and failover procedures</li>
                                    <li>Communicate with affected users via multiple channels</li>
                                    <li>Document incident for post-mortem analysis</li>
                                </ol>
                                <h5>Medium-term Actions (1-2 weeks):</h5>
                                <ol>
                                    <li>Conduct comprehensive infrastructure audit</li>
                                    <li>Implement additional monitoring and alerting</li>
                                    <li>Upgrade cloud infrastructure capacity</li>
                                    <li>Develop improved disaster recovery procedures</li>
                                </ol>
                            </div>
                        </div>

                        <div class="kri-detailed-item">
                            <div class="kri-header">
                                <span class="kri-metric">Competitor Activity</span>
                                <span class="kri-threshold">Major feature launch</span>
                            </div>
                            <div class="kri-mitigation">
                                <h5>Immediate Actions (Within 48 hours):</h5>
                                <ol>
                                    <li>Conduct competitive analysis of new features</li>
                                    <li>Assess impact on our value proposition</li>
                                    <li>Communicate our differentiators to customers</li>
                                    <li>Accelerate development of planned features</li>
                                </ol>
                                <h5>Medium-term Actions (2-4 weeks):</h5>
                                <ol>
                                    <li>Develop counter-positioning strategy</li>
                                    <li>Launch enhanced marketing campaigns</li>
                                    <li>Consider strategic partnerships or acquisitions</li>
                                    <li>Innovate beyond competitor capabilities</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="monitoring-section">
                    <h4>Review Schedule</h4>
                    <div class="review-schedule">
                        <div class="review-item">
                            <h5>Weekly Risk Review</h5>
                            <p>Management team reviews KRIs and immediate risks</p>
                        </div>
                        <div class="review-item">
                            <h5>Monthly Risk Assessment</h5>
                            <p>Comprehensive review of all risk categories and mitigation effectiveness</p>
                        </div>
                        <div class="review-item">
                            <h5>Quarterly Strategic Risk Review</h5>
                            <p>Board-level review of strategic risks and contingency plan updates</p>
                        </div>
                        <div class="review-item">
                            <h5>Annual Risk Framework Review</h5>
                            <p>Complete overhaul of risk assessment methodology and thresholds</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Implementation Timeline Section -->
<section class="section implementation-timeline" id="implementation">
    <div class="section-title">
        <h2>Implementation <span>Timeline</span></h2>
        <p>Detailed roadmap for business execution and growth</p>
    </div>

    <div class="timeline-content">
        <div class="timeline-overview">
            <h3>Strategic Implementation Phases</h3>
            <p>Our implementation strategy is divided into four key phases, each with specific milestones, deliverables, and success metrics. This phased approach ensures systematic growth while maintaining quality and sustainability.</p>
        </div>

        <div class="timeline-phases">
            <!-- Phase 1: Foundation -->
            <div class="phase-card phase-1">
                <div class="phase-header">
                    <h4>Phase 1: Foundation & Launch</h4>
                    <span class="phase-duration">Months 1-6 (Jan - Jun 2025)</span>
                </div>
                <div class="phase-content">
                    <div class="phase-objectives">
                        <h5>Key Objectives</h5>
                        <ul>
                            <li>Complete MVP development and testing</li>
                            <li>Establish legal and compliance framework</li>
                            <li>Launch pilot programs in 3 schools</li>
                            <li>Build core team and operational processes</li>
                        </ul>
                    </div>

                    <div class="phase-milestones">
                        <h5>Major Milestones</h5>
                        <div class="milestone-timeline">
                            <div class="milestone-item">
                                <span class="milestone-month">Month 1</span>
                                <span class="milestone-task">Complete POPIA compliance and BEE certification</span>
                            </div>
                            <div class="milestone-item">
                                <span class="milestone-month">Month 2</span>
                                <span class="milestone-task">Finalize MVP development and security testing</span>
                            </div>
                            <div class="milestone-item">
                                <span class="milestone-month">Month 3</span>
                                <span class="milestone-task">Launch pilot program in first school</span>
                            </div>
                            <div class="milestone-item">
                                <span class="milestone-month">Month 4</span>
                                <span class="milestone-task">Expand to 3 pilot schools, gather feedback</span>
                            </div>
                            <div class="milestone-item">
                                <span class="milestone-month">Month 5</span>
                                <span class="milestone-task">Implement feedback, prepare for public launch</span>
                            </div>
                            <div class="milestone-item">
                                <span class="milestone-month">Month 5</span>
                                <span class="milestone-task">Achieve breakeven: 1,500 users, R300K monthly revenue</span>
                            </div>
                            <div class="milestone-item">
                                <span class="milestone-month">Month 6</span>
                                <span class="milestone-task">Begin profit allocation strategy implementation</span>
                            </div>
                        </div>
                    </div>

                    <div class="phase-metrics">
                        <h5>Success Metrics</h5>
                        <div class="metrics-grid">
                            <div class="metric-item">
                                <span class="metric-value">1,500</span>
                                <span class="metric-label">Paying Users</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-value">3</span>
                                <span class="metric-label">Pilot <span class="dynamic-content" data-field="clientType">Clients</span></span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-value">R300K</span>
                                <span class="metric-label">Monthly Revenue</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-value">95%</span>
                                <span class="metric-label">User Satisfaction</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Phase 2: Growth -->
            <div class="phase-card phase-2">
                <div class="phase-header">
                    <h4>Phase 2: Market Penetration</h4>
                    <span class="phase-duration">Months 7-12 (Jul - Dec 2025)</span>
                </div>
                <div class="phase-content">
                    <div class="phase-objectives">
                        <h5>Key Objectives</h5>
                        <ul>
                            <li>Scale beyond breakeven to 3,000+ users</li>
                            <li>Implement profit allocation strategy</li>
                            <li>Launch additional service offerings</li>
                            <li>Expand to 15 <span class="dynamic-content" data-field="clientType">client</span> partnerships</li>
                            <li>Reinvest profits for organic growth acceleration</li>
                        </ul>
                    </div>

                    <div class="phase-milestones">
                        <h5>Major Milestones</h5>
                        <div class="milestone-timeline">
                            <div class="milestone-item">
                                <span class="milestone-month">Month 7</span>
                                <span class="milestone-task">Scale to 2,000 users, launch additional services</span>
                            </div>
                            <div class="milestone-item">
                                <span class="milestone-month">Month 8</span>
                                <span class="milestone-task">Implement profit allocation, expand to 10 <span class="dynamic-content" data-field="clientType">organizations</span></span>
                            </div>
                            <div class="milestone-item">
                                <span class="milestone-month">Month 9</span>
                                <span class="milestone-task">Reach 2,500 users, expand service portfolio</span>
                            </div>
                            <div class="milestone-item">
                                <span class="milestone-month">Month 10</span>
                                <span class="milestone-task">Launch referral program, R&D investments</span>
                            </div>
                            <div class="milestone-item">
                                <span class="milestone-month">Month 11</span>
                                <span class="milestone-task">Reach 3,000 users, contingency fund building</span>
                            </div>
                            <div class="milestone-item">
                                <span class="milestone-month">Month 12</span>
                                <span class="milestone-task">Achieve 3,000+ users, 15 <span class="dynamic-content" data-field="clientType">client</span> partnerships</span>
                            </div>
                        </div>
                    </div>

                    <div class="phase-metrics">
                        <h5>Success Metrics</h5>
                        <div class="metrics-grid">
                            <div class="metric-item">
                                <span class="metric-value">3,000</span>
                                <span class="metric-label">Active Users</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-value">15</span>
                                <span class="metric-label"><span class="dynamic-content" data-field="clientType">Client</span> Partners</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-value">R600K</span>
                                <span class="metric-label">Monthly Revenue</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-value">R180K</span>
                                <span class="metric-label">Monthly Profit</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Phase 3: Expansion -->
            <div class="phase-card phase-3">
                <div class="phase-header">
                    <h4>Phase 3: Rapid Expansion</h4>
                    <span class="phase-duration">Year 2 (Jan - Dec 2026)</span>
                </div>
                <div class="phase-content">
                    <div class="phase-objectives">
                        <h5>Key Objectives</h5>
                        <ul>
                            <li>Scale to 5,000+ users across 3 provinces</li>
                            <li>Launch premium features and enterprise solutions</li>
                            <li>Establish market leadership position</li>
                            <li>Prepare for international expansion</li>
                        </ul>
                    </div>

                    <div class="phase-milestones">
                        <h5>Major Milestones</h5>
                        <div class="milestone-timeline">
                            <div class="milestone-item">
                                <span class="milestone-month">Q1 2026</span>
                                <span class="milestone-task">Launch in Western Cape and Eastern Cape</span>
                            </div>
                            <div class="milestone-item">
                                <span class="milestone-month">Q2 2026</span>
                                <span class="milestone-task">Reach 3,000 users, launch premium tier</span>
                            </div>
                            <div class="milestone-item">
                                <span class="milestone-month">Q3 2026</span>
                                <span class="milestone-task">50 school partnerships, enterprise features</span>
                            </div>
                            <div class="milestone-item">
                                <span class="milestone-month">Q4 2026</span>
                                <span class="milestone-task">5,000+ users, market leadership established</span>
                            </div>
                        </div>
                    </div>

                    <div class="phase-metrics">
                        <h5>Success Metrics</h5>
                        <div class="metrics-grid">
                            <div class="metric-item">
                                <span class="metric-value">5,000+</span>
                                <span class="metric-label">Active Users</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-value">50</span>
                                <span class="metric-label">School Partners</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-value">R1M</span>
                                <span class="metric-label">Monthly Revenue</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-value">30%</span>
                                <span class="metric-label">Market Share</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Phase 4: Maturity -->
            <div class="phase-card phase-4">
                <div class="phase-header">
                    <h4>Phase 4: Market Leadership & Exit</h4>
                    <span class="phase-duration">Year 3+ (2027 onwards)</span>
                </div>
                <div class="phase-content">
                    <div class="phase-objectives">
                        <h5>Key Objectives</h5>
                        <ul>
                            <li>Achieve market dominance in South Africa</li>
                            <li>Expand to other African countries</li>
                            <li>Develop advanced AI capabilities</li>
                            <li>Prepare for strategic exit opportunities</li>
                        </ul>
                    </div>

                    <div class="phase-milestones">
                        <h5>Major Milestones</h5>
                        <div class="milestone-timeline">
                            <div class="milestone-item">
                                <span class="milestone-month">Q1 2027</span>
                                <span class="milestone-task">Launch in Botswana and Namibia</span>
                            </div>
                            <div class="milestone-item">
                                <span class="milestone-month">Q2 2027</span>
                                <span class="milestone-task">10,000+ users, advanced AI features</span>
                            </div>
                            <div class="milestone-item">
                                <span class="milestone-month">Q3 2027</span>
                                <span class="milestone-task">Strategic partnerships with publishers</span>
                            </div>
                            <div class="milestone-item">
                                <span class="milestone-month">Q4 2027</span>
                                <span class="milestone-task">Evaluate exit opportunities (IPO/Acquisition)</span>
                            </div>
                        </div>
                    </div>

                    <div class="phase-metrics">
                        <h5>Success Metrics</h5>
                        <div class="metrics-grid">
                            <div class="metric-item">
                                <span class="metric-value">10,000+</span>
                                <span class="metric-label">Active Users</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-value">3</span>
                                <span class="metric-label">Countries</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-value">R2M+</span>
                                <span class="metric-label">Monthly Revenue</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-value">50%</span>
                                <span class="metric-label">Market Share</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Financial Projections Section -->
<section class="section financial" id="financial">
    <div class="section-title">
        <h2>Financial <span>Projections</span></h2>
        <p>Our path to sustainable growth</p>
    </div>
    <div class="financial-content">
        <div class="startup-costs">
            <h3>Startup Costs & Resource Allocation</h3>
            <div class="cost-explanation">
                <p><strong>Strategic Advantage:</strong> Our team already owns all necessary hardware and development equipment, allowing us to focus our R80,000 capital entirely on cloud services and operational expenses rather than equipment purchases.</p>
            </div>
            <div class="cost-items">
                <div class="cost-item">
                    <span class="amount">R40,000</span>
                    <span class="category">Cloud Services & Infrastructure (AWS/Azure for 12 months)</span>
                </div>
                <div class="cost-item">
                    <span class="amount">R20,000</span>
                    <span class="category">Software Licenses & Development Tools (AI APIs, databases)</span>
                </div>
                <div class="cost-item">
                    <span class="amount">R15,000</span>
                    <span class="category">Initial Marketing & Legal Setup (POPIA compliance, BEE certification)</span>
                </div>
                <div class="cost-item">
                    <span class="amount">R5,000</span>
                    <span class="category">Domain, SSL, Security Services & Backup Systems</span>
                </div>
                <div class="cost-item total">
                    <span class="amount dynamic-content" data-field="initialCapital">R80,000</span>
                    <span class="category">Total Initial Capital Available</span>
                </div>
            </div>

            <div class="existing-resources">
                <h4>Existing Resources (No Additional Cost)</h4>
                <div class="resource-items">
                    <div class="resource-item">
                        <span class="resource-type">Development Hardware</span>
                        <span class="resource-value">8 High-spec laptops/workstations</span>
                    </div>
                    <div class="resource-item">
                        <span class="resource-type">Testing Devices</span>
                        <span class="resource-value">Multiple smartphones, tablets for testing</span>
                    </div>
                    <div class="resource-item">
                        <span class="resource-type">Office Equipment</span>
                        <span class="resource-value">Monitors, networking equipment, furniture</span>
                    </div>
                    <div class="resource-item">
                        <span class="resource-type">Software Licenses</span>
                        <span class="resource-value">Development IDEs, design tools (personal licenses)</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="revenue-projections">
            <h3>3-Year Revenue Projections</h3>
            <div class="revenue-breakdown">
                <div class="revenue-source lumerous">
                    <h4><span class="dynamic-content" data-field="productName">Lumerous</span> (Primary Revenue Stream)</h4>
                    <div class="projection-items">
                        <div class="projection-item">
                            <span class="year">Year 1</span>
                            <span class="amount">R4.32 million</span>
                            <span class="details">1,500 users by Month 5, scaling to 1,800 users by year-end</span>
                        </div>
                        <div class="projection-item">
                            <span class="year">Year 2</span>
                            <span class="amount">R7.2 million</span>
                            <span class="details">3,000 users average, R200/month per user</span>
                        </div>
                        <div class="projection-item">
                            <span class="year">Year 3</span>
                            <span class="amount">R14.4 million</span>
                            <span class="details">6,000 users average, R200/month per user</span>
                        </div>
                    </div>
                </div>

                <div class="revenue-source additional">
                    <h4>Additional Services (Supplementary Revenue)</h4>
                    <div class="projection-items">
                        <div class="projection-item">
                            <span class="year">Year 1</span>
                            <span class="amount">R1.2 million</span>
                            <span class="details">POPIA SaaS, e-commerce projects, school partnerships</span>
                        </div>
                        <div class="projection-item">
                            <span class="year">Year 2</span>
                            <span class="amount">R2.4 million</span>
                            <span class="details">Expanded service offerings and client base</span>
                        </div>
                        <div class="projection-item">
                            <span class="year">Year 3</span>
                            <span class="amount">R4.8 million</span>
                            <span class="details">Mature service portfolio with premium offerings</span>
                        </div>
                    </div>
                </div>

                <div class="revenue-total">
                    <h4>Total Combined Revenue</h4>
                    <div class="projection-items">
                        <div class="projection-item total">
                            <span class="year">Year 1</span>
                            <span class="amount">R4.32 million</span>
                            <span class="details"><span class="dynamic-content" data-field="productName">Lumerous</span> + Additional Services</span>
                        </div>
                        <div class="projection-item total">
                            <span class="year">Year 2</span>
                            <span class="amount">R7.2 million</span>
                            <span class="details">Strong growth across all revenue streams</span>
                        </div>
                        <div class="projection-item total">
                            <span class="year">Year 3</span>
                            <span class="amount">R14.4 million</span>
                            <span class="details">Market leadership and diversified portfolio</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="pricing-model">
            <h3>Pricing Structure</h3>
            <div class="pricing-items">
                <div class="pricing-item lumerous-pricing">
                    <span class="service dynamic-content" data-field="productName">Lumerous Platform</span>
                    <span class="price dynamic-content" data-field="pricePerUser">R200/month per user</span>
                    <span class="details">Individual subscriptions - Primary revenue stream</span>
                </div>
                <div class="pricing-item">
                    <span class="service">Enterprise Licenses</span>
                    <span class="price">R5,000/month per <span class="dynamic-content" data-field="clientType">organization</span></span>
                    <span class="details">Institutional partnerships</span>
                </div>
                <div class="pricing-item">
                    <span class="service">POPIA SaaS</span>
                    <span class="price">R2,000–R5,000/month</span>
                    <span class="details">Based on business size</span>
                </div>
                <div class="pricing-item">
                    <span class="service">E-commerce Sites</span>
                    <span class="price">R50,000 one-time + R2,000/month maintenance</span>
                </div>
            </div>
        </div>

        <div class="profit-allocation-strategy">
            <h3>Strategic Profit Allocation Framework</h3>
            <div class="allocation-intro">
                <p>Once <span class="dynamic-content" data-field="productName">Lumerous</span> achieves <span class="dynamic-content" data-field="monthlyCosts">R300,000/month</span> (Month 5), all profits will be strategically allocated across five key areas to ensure sustainable growth, risk mitigation, and long-term financial stability. This disciplined approach creates multiple value streams while protecting the business.</p>
            </div>

            <div class="allocation-breakdown">
                <div class="allocation-card contingency">
                    <div class="allocation-header">
                        <h4>Contingency Fund</h4>
                        <span class="allocation-percentage">30%</span>
                    </div>
                    <div class="allocation-content">
                        <p><strong>Purpose:</strong> Risk mitigation and financial security</p>
                        <ul>
                            <li>Emergency operational expenses</li>
                            <li>Market downturn protection</li>
                            <li>Unexpected opportunity funding</li>
                            <li>Legal and compliance reserves</li>
                        </ul>
                        <div class="allocation-cap">
                            <h5>Cap Strategy</h5>
                            <p><strong>Maximum:</strong> 2 years of operating costs (R7.2M)</p>
                            <p><strong>Overflow:</strong> Excess funds redirect to Passive Income Fund</p>
                        </div>
                    </div>
                </div>

                <div class="allocation-card rd">
                    <div class="allocation-header">
                        <h4>Research & Development</h4>
                        <span class="allocation-percentage">20%</span>
                    </div>
                    <div class="allocation-content">
                        <p><strong>Purpose:</strong> Innovation and competitive advantage</p>
                        <ul>
                            <li>AI algorithm enhancement</li>
                            <li>New feature development</li>
                            <li>Technology infrastructure upgrades</li>
                            <li>Patent applications and IP protection</li>
                        </ul>
                    </div>
                </div>

                <div class="allocation-card growth">
                    <div class="allocation-header">
                        <h4>Company Growth</h4>
                        <span class="allocation-percentage">20%</span>
                    </div>
                    <div class="allocation-content">
                        <p><strong>Purpose:</strong> Infrastructure and capability enhancement</p>
                        <ul>
                            <li>Office space expansion and furniture</li>
                            <li>High-performance equipment and hardware</li>
                            <li>Professional development and training</li>
                            <li>Software licenses and productivity tools</li>
                        </ul>
                    </div>
                </div>

                <div class="allocation-card culture">
                    <div class="allocation-header">
                        <h4>Team Building & Culture</h4>
                        <span class="allocation-percentage">15%</span>
                    </div>
                    <div class="allocation-content">
                        <p><strong>Purpose:</strong> Employee satisfaction and retention</p>
                        <ul>
                            <li>Performance bonuses and incentives</li>
                            <li>Team building activities and retreats</li>
                            <li>Employee wellness programs</li>
                            <li>Recognition and reward systems</li>
                        </ul>
                    </div>
                </div>

                <div class="allocation-card passive">
                    <div class="allocation-header">
                        <h4>Passive Income Fund</h4>
                        <span class="allocation-percentage">15%</span>
                    </div>
                    <div class="allocation-content">
                        <p><strong>Purpose:</strong> Portfolio diversification and passive income</p>
                        <ul>
                            <li>Real estate investments</li>
                            <li>Stock market portfolios</li>
                            <li>Government and corporate bonds</li>
                            <li>Dividend-paying investments</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Milestones Section -->
<section class="section milestones" id="milestones">
    <div class="section-title">
        <h2>Key <span>Milestones</span></h2>
        <p>Our roadmap to success</p>
    </div>
    <div class="timeline">
        <div class="timeline-item">
            <div class="timeline-date">Q3 2025</div>
            <div class="timeline-content">
                <h3>Launch POPIA Compliance SaaS</h3>
                <p>Secure 10 pilot clients for initial testing and feedback</p>
            </div>
        </div>
        <div class="timeline-item">
            <div class="timeline-date">Q4 2025</div>
            <div class="timeline-content">
                <h3>Develop E-commerce Platform</h3>
                <p>Sign 5 retail clients for custom e-commerce solutions</p>
            </div>
        </div>
        <div class="timeline-item">
            <div class="timeline-date">Q1 2026</div>
            <div class="timeline-content">
                <h3>School Partnerships</h3>
                <p>Establish partnerships with 5 schools for hardware and software solutions</p>
            </div>
        </div>
        <div class="timeline-item">
            <div class="timeline-date">Q3 2026</div>
            <div class="timeline-content">
                <h3>Scale Operations</h3>
                <p>Reach 100 SaaS clients and 20 e-commerce projects</p>
            </div>
        </div>
    </div>
</section>

<!-- Contact Section -->
<footer id="contact">
    <div class="footer-content">
        <div class="footer-column">
            <h3>Solving Tomorrow</h3>
            <p>Empowering South African businesses through AI-powered SaaS solutions.</p>
            <p>BEE Level 1 Contributor • POPIA Compliant</p>
        </div>
        
        <div class="footer-column">
            <h3>Contact Us</h3>
            <p><i class="fas fa-map-marker-alt"></i> 28 Meridian Drive, Durban</p>
            <p><i class="fas fa-phone"></i> +27 31 555 1234</p>
            <p><i class="fas fa-envelope"></i> <EMAIL></p>
        </div>
        
        <div class="footer-column">
            <h3>Quick Links</h3>
            <a href="#services">Our Services</a>
            <a href="#lumerous">Lumerous Platform</a>
            <a href="#revenue">Revenue Model</a>
            <a href="privacy-policy.html">Privacy Policy</a>
            <a href="terms-of-service.html">Terms of Service</a>
            <a href="careers.html">Careers</a>
        </div>

        <div class="footer-column">
            <h3>Business Information</h3>
            <a href="#executive-summary">Business Plan</a>
            <a href="#financial">Financial Projections</a>
            <a href="#implementation">Implementation Timeline</a>
            <a href="mailto:<EMAIL>">Business Inquiries</a>
        </div>
    </div>
    
    <div class="footer-bottom">
        <p>&copy; 2025 Solving Tomorrow (Pty) Ltd. All rights reserved.</p>
    </div>
</footer>

<script>
    // Header Scroll Effect
    const header = document.getElementById('header');

    window.addEventListener('scroll', () => {
        if (window.scrollY > 50) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
    });
    
    // Smooth Scrolling
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            
            window.scrollTo({
                top: targetElement.offsetTop - 80,
                behavior: 'smooth'
            });
            
            // Close mobile menu if open
            navLinks.classList.remove('active');
        });
    });
    
    // Revenue Chart
    const ctx = document.getElementById('revenueChart').getContext('2d');
    let revenueChart;
    
    // Chart Data
    const chartData = {
        optimistic: {
            labels: ['Month 1', 'Month 2', 'Month 3', 'Month 4', 'Month 5', 'Month 6', 'Month 7', 'Month 8', 'Month 9', 'Month 10', 'Month 11', 'Month 12'],
            revenue: [100000, 200000, 300000, 400000, 500000, 600000, 700000, 800000, 900000, 1000000, 1100000, 1200000],
            breakeven: 3,
            users: [500, 1000, 1500, 2000, 2500, 3000, 3500, 4000, 4500, 5000, 5500, 6000]
        },
        realistic: {
            labels: ['Month 1', 'Month 2', 'Month 3', 'Month 4', 'Month 5', 'Month 6', 'Month 7', 'Month 8', 'Month 9', 'Month 10', 'Month 11', 'Month 12'],
            revenue: [40000, 80000, 120000, 160000, 200000, 240000, 280000, 320000, 360000, 400000, 440000, 480000],
            breakeven: 8,
            users: [200, 400, 600, 800, 1000, 1200, 1400, 1600, 1800, 2000, 2200, 2400]
        },
        conservative: {
            labels: ['Month 1', 'Month 2', 'Month 3', 'Month 4', 'Month 5', 'Month 6', 'Month 7', 'Month 8', 'Month 9', 'Month 10', 'Month 11', 'Month 12'],
            revenue: [20000, 40000, 60000, 80000, 100000, 120000, 140000, 160000, 180000, 200000, 220000, 240000],
            breakeven: 15,
            users: [100, 200, 300, 400, 500, 600, 700, 800, 900, 1000, 1100, 1200]
        }
    };
    
    // Initialize Chart
    function initChart(scenario) {
        const data = chartData[scenario];
        
        if (revenueChart) {
            revenueChart.destroy();
        }
        
        revenueChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.labels,
                datasets: [
                    {
                        label: 'Monthly Revenue (R)',
                        data: data.revenue,
                        borderColor: '#FF6B35',
                        backgroundColor: 'rgba(255, 107, 53, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.3
                    },
                    {
                        label: 'Breakeven Point',
                        data: Array(data.labels.length).fill(300000),
                        borderColor: '#F7F7F7',
                        borderWidth: 2,
                        borderDash: [5, 5],
                        fill: false,
                        pointRadius: 0
                    }
                ]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: `Revenue Projection: ${scenario.charAt(0).toUpperCase() + scenario.slice(1)} Scenario`,
                        color: '#F7F7F7',
                        font: {
                            size: 18
                        }
                    },
                    legend: {
                        labels: {
                            color: '#F7F7F7',
                            font: {
                                size: 14
                            }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    label += new Intl.NumberFormat('en-ZA', { style: 'currency', currency: 'ZAR', maximumFractionDigits: 0 }).format(context.parsed.y);
                                }
                                return label;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: '#F7F7F7'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    },
                    y: {
                        ticks: {
                            color: '#F7F7F7',
                            callback: function(value) {
                                return new Intl.NumberFormat('en-ZA', { style: 'currency', currency: 'ZAR', maximumFractionDigits: 0 }).format(value);
                            }
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    }
                }
            }
        });
    }
    
    // Scenario Buttons
    const scenarioButtons = document.querySelectorAll('.chart-controls button');
    
    scenarioButtons.forEach(button => {
        button.addEventListener('click', () => {
            scenarioButtons.forEach(btn => btn.classList.remove('active'));
            button.classList.add('active');
            initChart(button.dataset.scenario);
        });
    });
    
    // Initialize with realistic scenario
    initChart('realistic');
    
    // Projection Calculator
    const calculateBtn = document.getElementById('calculateBtn');
    
    calculateBtn.addEventListener('click', () => {
        const userGrowth = parseInt(document.getElementById('userGrowth').value) || 0;
        const schoolGrowth = parseInt(document.getElementById('schoolGrowth').value) || 0;
        const churnRate = (parseInt(document.getElementById('churnRate').value) || 0) / 100;
        const premiumUpsell = (parseInt(document.getElementById('premiumUpsell').value) || 0) / 100;

        // Validation
        if (userGrowth <= 0) {
            alert('Please enter a valid number of new users per month (greater than 0)');
            return;
        }

        const fixedCosts = 300000;
        let breakevenMonth = 0;
        let activeUsers = 0;
        let totalSchools = 0;

        // Calculate breakeven month by month
        for (let month = 1; month <= 24; month++) {
            // Calculate active users (new users minus churned users)
            const newUsers = userGrowth;
            const existingUsers = activeUsers * (1 - churnRate); // Apply churn to existing users
            activeUsers = existingUsers + newUsers;

            // Calculate schools (quarterly growth)
            if (month % 3 === 0) { // Every 3 months
                totalSchools += schoolGrowth;
            }

            // Calculate monthly revenue
            const userRevenue = activeUsers * 200;
            const schoolRevenue = totalSchools * 5000;
            const premiumRevenue = activeUsers * premiumUpsell * 50;
            const totalMonthlyRevenue = userRevenue + schoolRevenue + premiumRevenue;

            // Check if we've reached breakeven
            if (totalMonthlyRevenue >= fixedCosts && breakevenMonth === 0) {
                breakevenMonth = month;
            }
        }

        // Calculate Year 1 and Year 2 projections
        let year1Users = 0;
        let year1Schools = 0;
        let year2Users = 0;
        let year2Schools = 0;

        // Year 1 calculation (12 months)
        let tempUsers = 0;
        for (let month = 1; month <= 12; month++) {
            tempUsers = tempUsers * (1 - churnRate) + userGrowth;
            if (month % 3 === 0) year1Schools += schoolGrowth;
        }
        year1Users = tempUsers;

        // Year 2 calculation (24 months total)
        for (let month = 13; month <= 24; month++) {
            tempUsers = tempUsers * (1 - churnRate) + userGrowth;
            if (month % 3 === 0) year2Schools += schoolGrowth;
        }
        year2Users = tempUsers;
        year2Schools = year1Schools + year2Schools;

        const year1Revenue = (year1Users * 200 * 12) + (year1Schools * 5000 * 12) + (year1Users * premiumUpsell * 50 * 12);
        const year2Revenue = (year2Users * 200 * 12) + (year2Schools * 5000 * 12) + (year2Users * premiumUpsell * 50 * 12);

        // Update results
        document.getElementById('breakevenMonth').textContent = breakevenMonth > 0 ? `Month ${breakevenMonth}` : 'Beyond 24 months';
        document.getElementById('year1Revenue').textContent = new Intl.NumberFormat('en-ZA', {
            style: 'currency',
            currency: 'ZAR',
            maximumFractionDigits: 0
        }).format(year1Revenue);
        document.getElementById('year2Revenue').textContent = new Intl.NumberFormat('en-ZA', {
            style: 'currency',
            currency: 'ZAR',
            maximumFractionDigits: 0
        }).format(year2Revenue);

        // Calculate users at breakeven
        let usersAtBreakeven = 0;
        if (breakevenMonth > 0) {
            let tempUsersBreakeven = 0;
            for (let month = 1; month <= breakevenMonth; month++) {
                tempUsersBreakeven = tempUsersBreakeven * (1 - churnRate) + userGrowth;
            }
            usersAtBreakeven = Math.floor(tempUsersBreakeven);
        }

        document.getElementById('breakevenUsers').textContent = usersAtBreakeven.toLocaleString();

        // Show results
        document.getElementById('calculatorResults').style.display = 'block';

        // Debug information (can be removed in production)
        console.log('Calculator Results:', {
            userGrowth,
            schoolGrowth,
            churnRate: churnRate * 100 + '%',
            premiumUpsell: premiumUpsell * 100 + '%',
            breakevenMonth,
            usersAtBreakeven,
            year1Revenue: year1Revenue.toLocaleString(),
            year2Revenue: year2Revenue.toLocaleString()
        });
    });

    // Sidebar functionality
    const sidebar = document.getElementById('sidebar');
    const sidebarToggle = document.getElementById('sidebarToggle');
    const mainContent = document.getElementById('mainContent');
    const navLinks = document.querySelectorAll('.nav-link');

    // Toggle sidebar on mobile
    sidebarToggle.addEventListener('click', () => {
        sidebar.classList.toggle('show');
    });

    // Close sidebar when clicking outside on mobile
    document.addEventListener('click', (e) => {
        if (window.innerWidth <= 768 &&
            !sidebar.contains(e.target) &&
            !sidebarToggle.contains(e.target)) {
            sidebar.classList.remove('show');
        }
    });

    // Active navigation highlighting
    function updateActiveNav() {
        const sections = document.querySelectorAll('section[id]');
        const scrollPos = window.scrollY + 100;

        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.offsetHeight;
            const sectionId = section.getAttribute('id');

            if (scrollPos >= sectionTop && scrollPos < sectionTop + sectionHeight) {
                navLinks.forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('href') === `#${sectionId}`) {
                        link.classList.add('active');
                    }
                });
            }
        });
    }

    // Update active nav on scroll
    window.addEventListener('scroll', updateActiveNav);

    // Update active nav on page load
    updateActiveNav();

    // Smooth scrolling for navigation links
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const targetId = link.getAttribute('href').substring(1);
            const targetSection = document.getElementById(targetId);

            if (targetSection) {
                const offsetTop = targetSection.offsetTop - 80; // Account for fixed header
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }

            // Close sidebar on mobile after clicking
            if (window.innerWidth <= 768) {
                sidebar.classList.remove('show');
            }
        });
    });

    // Business Plan Configurator
    const configurator = {
        // Product type descriptions
        productDescriptions: {
            'education': 'an AI-powered learning platform designed for educational institutions',
            'ecommerce': 'a comprehensive e-commerce solution with AI-driven analytics',
            'compliance': 'an automated compliance management system',
            'productivity': 'a productivity enhancement tool with smart automation',
            'analytics': 'an advanced analytics platform with predictive insights',
            'communication': 'a communication and collaboration platform',
            'custom': 'a custom software solution tailored to specific business needs'
        },

        // Product categories
        productCategories: {
            'education': 'Education Platform',
            'ecommerce': 'E-commerce Solution',
            'compliance': 'Compliance Software',
            'productivity': 'Productivity Tool',
            'analytics': 'Analytics Platform',
            'communication': 'Communication Platform',
            'custom': 'Custom Software'
        },

        // Product taglines
        productTaglines: {
            'education': 'Revolutionizing education through AI technology',
            'ecommerce': 'Powering next-generation e-commerce experiences',
            'compliance': 'Automating compliance for modern businesses',
            'productivity': 'Enhancing productivity through smart automation',
            'analytics': 'Transforming data into actionable insights',
            'communication': 'Connecting teams with intelligent communication',
            'custom': 'Custom solutions for unique business challenges'
        },

        // Market descriptions
        marketDescriptions: {
            'education': 'the South African education market',
            'smb': 'small and medium businesses across South Africa',
            'enterprise': 'large enterprise organizations',
            'healthcare': 'the healthcare and medical sector',
            'finance': 'financial services and banking institutions',
            'retail': 'retail and consumer goods companies',
            'government': 'government agencies and public sector'
        },

        // Industry-specific content - Research-based and sector-tailored
        industryContent: {
            'education': {
                competitors: ['Mindset Learn', 'GetSmarter', 'Coursera for Business', 'Khan Academy', 'Udemy Business'],
                advantages: ['CAPS/IEB curriculum alignment', 'Local language support (Afrikaans, Zulu, Xhosa)', 'Offline-capable mobile app', 'Teacher dashboard analytics', 'Parent progress reports'],
                threats: ['Google Classroom expansion', 'Microsoft Education suite', 'Free Khan Academy content', 'Traditional tutoring centers', 'Budget cuts in education'],
                opportunities: ['Post-COVID digital learning adoption', 'Government R2.5B EdTech investment', 'Teacher shortage (30,000 deficit)', 'Mobile penetration growth', 'Assessment digitization'],
                valueProposition: 'AI-powered personalized learning platform aligned with South African curriculum standards',
                usp: 'Only CAPS/IEB-aligned AI tutor with offline capabilities and local language support',
                marketSize: 'R2.8 billion',
                targetSegment: 'Grade 8-12 students, teachers, and schools',
                keyFeatures: ['AI-powered personalized tutoring', '100% CAPS/IEB curriculum alignment', 'Progress tracking and analytics', 'Gamified learning experience'],
                clientType: 'schools',
                serviceModel: 'freemium EdTech platform with premium subscriptions',
                implementationTime: '2-4 months per school district',
                successMetric: 'improved academic performance and engagement rates',
                strengths: ['Deep curriculum expertise with CAPS/IEB alignment', 'AI-powered personalized learning paths', 'Local language support (11 official languages)', 'Mobile-first design for data constraints', 'Teacher training and support programs'],
                weaknesses: ['New brand in established market', 'Limited content library vs 15-year competitors', 'Dependency on internet connectivity', 'Need for teacher adoption and training', 'Competition with free resources'],
                marketTrends: ['78% of schools now use digital learning tools', '95% smartphone penetration in target age group', 'Government push for digital transformation', 'Increased focus on STEM education', 'Growing demand for personalized learning']
            },
            'ecommerce': {
                competitors: ['Shopify', 'WooCommerce', 'Magento'],
                advantages: ['Local payment integration', 'South African tax compliance', 'Multi-language support', 'Mobile optimization'],
                threats: ['International platforms', 'Custom development agencies', 'Marketplace dominance'],
                opportunities: ['Growing online retail market', 'SME digitization', 'Mobile commerce growth'],
                valueProposition: 'Complete e-commerce solution tailored for South African businesses',
                usp: 'Built-in compliance with South African regulations and payment systems',
                marketSize: 'R1.5 billion',
                targetSegment: 'SMEs, retailers, and entrepreneurs',
                keyFeatures: ['Payment gateway integration', 'Inventory management', 'Analytics dashboard', 'Mobile-responsive design'],
                clientType: 'businesses',
                serviceModel: 'SaaS e-commerce platform',
                implementationTime: '2-4 weeks per store',
                successMetric: 'online sales growth and conversion rates'
            },
            'compliance': {
                competitors: ['Thomson Reuters', 'Compliance.ai', 'MetricStream'],
                advantages: ['Local regulatory expertise', 'POPIA specialization', 'Industry-specific modules', 'Cost-effective solution'],
                threats: ['International compliance giants', 'In-house legal teams', 'Manual compliance processes'],
                opportunities: ['POPIA implementation requirements', 'Increasing regulatory complexity', 'Digital transformation'],
                valueProposition: 'Automated compliance management for South African regulations',
                usp: 'Specialized POPIA compliance automation with local legal expertise',
                marketSize: 'R800 million',
                targetSegment: 'Businesses, legal firms, and compliance officers',
                keyFeatures: ['Automated reporting', 'Risk assessment', 'Document management', 'Audit trails'],
                clientType: 'organizations',
                serviceModel: 'compliance automation platform',
                implementationTime: '4-8 weeks per organization',
                successMetric: 'compliance score improvement and audit readiness'
            },
            'productivity': {
                competitors: ['Microsoft 365', 'Google Workspace', 'Slack'],
                advantages: ['Industry-specific workflows', 'Local data hosting', 'Custom integrations', 'Competitive pricing'],
                threats: ['Tech giants with established ecosystems', 'Free alternatives', 'Custom internal tools'],
                opportunities: ['Remote work adoption', 'Digital transformation', 'Productivity optimization needs'],
                valueProposition: 'Smart productivity tools designed for South African business workflows',
                usp: 'AI-powered automation tailored to local business practices and regulations',
                marketSize: 'R1.2 billion',
                targetSegment: 'Businesses, teams, and knowledge workers',
                keyFeatures: ['Workflow automation', 'Team collaboration', 'Performance analytics', 'Integration hub'],
                clientType: 'teams',
                serviceModel: 'productivity enhancement platform',
                implementationTime: '1-3 weeks per team',
                successMetric: 'productivity gains and workflow efficiency'
            },
            'analytics': {
                competitors: ['Tableau', 'Power BI', 'Google Analytics'],
                advantages: ['Industry-specific insights', 'Local data compliance', 'Predictive analytics', 'User-friendly interface'],
                threats: ['Enterprise analytics giants', 'Open-source solutions', 'In-house data teams'],
                opportunities: ['Data-driven decision making trend', 'AI/ML adoption', 'Regulatory reporting needs'],
                valueProposition: 'Advanced analytics platform with AI-powered insights for African markets',
                usp: 'Predictive analytics specifically trained on South African market data',
                marketSize: 'R900 million',
                targetSegment: 'Enterprises, analysts, and decision makers',
                keyFeatures: ['Predictive modeling', 'Real-time dashboards', 'Custom reporting', 'Data visualization'],
                clientType: 'enterprises',
                serviceModel: 'analytics and insights platform',
                implementationTime: '2-6 weeks per deployment',
                successMetric: 'data-driven decision accuracy and ROI improvement'
            },
            'communication': {
                competitors: ['WhatsApp Business', 'Zoom', 'Microsoft Teams'],
                advantages: ['Local hosting', 'Multi-language support', 'Industry compliance', 'Affordable pricing'],
                threats: ['Global communication platforms', 'Free messaging apps', 'Video conferencing giants'],
                opportunities: ['Remote work growth', 'Team collaboration needs', 'Security concerns with global platforms'],
                valueProposition: 'Secure communication platform designed for South African businesses',
                usp: 'Local data hosting with enterprise-grade security and compliance',
                marketSize: 'R600 million',
                targetSegment: 'Businesses, teams, and remote workers',
                keyFeatures: ['Secure messaging', 'Video conferencing', 'File sharing', 'Team channels'],
                clientType: 'organizations',
                serviceModel: 'communication and collaboration platform',
                implementationTime: '1-2 weeks per organization',
                successMetric: 'communication efficiency and team collaboration improvement'
            },
            'custom': {
                competitors: ['Custom development agencies', 'Freelance developers', 'In-house teams'],
                advantages: ['Rapid development', 'Industry expertise', 'Ongoing support', 'Cost-effective solutions'],
                threats: ['Large development agencies', 'Offshore development', 'DIY platforms'],
                opportunities: ['Digital transformation needs', 'Legacy system modernization', 'Automation requirements'],
                valueProposition: 'Custom software solutions tailored to specific business needs',
                usp: 'Rapid development with deep understanding of South African business requirements',
                marketSize: 'R2.0 billion',
                targetSegment: 'Businesses with unique requirements',
                keyFeatures: ['Custom development', 'System integration', 'Ongoing support', 'Scalable architecture'],
                clientType: 'clients',
                serviceModel: 'custom software development',
                implementationTime: '3-12 months per project',
                successMetric: 'project delivery success and client satisfaction'
            }
        },

        // Initialize configurator
        init() {
            this.bindEvents();
            this.loadDefaults();
        },

        // Bind event listeners
        bindEvents() {
            document.getElementById('updatePlan').addEventListener('click', () => this.updateBusinessPlan());
            document.getElementById('exportPDF').addEventListener('click', () => this.exportToPDF());
            document.getElementById('resetDefaults').addEventListener('click', () => this.resetToDefaults());
            document.getElementById('exportConfig').addEventListener('click', () => this.exportConfiguration());

            // Real-time updates on input change
            const inputs = document.querySelectorAll('#configurator input, #configurator select');
            inputs.forEach(input => {
                input.addEventListener('change', () => this.calculateMetrics());
            });
        },

        // Load default values
        loadDefaults() {
            this.calculateMetrics();
        },

        // Calculate key metrics
        calculateMetrics() {
            const pricePerUser = parseFloat(document.getElementById('pricePerUser').value) || 200;
            const monthlyCosts = parseFloat(document.getElementById('monthlyCosts').value) || 300000;
            const growthRate = parseFloat(document.getElementById('growthRate').value) || 15;
            const breakevenMonth = parseInt(document.getElementById('breakevenMonth').value) || 5;
            const marketSize = parseFloat(document.getElementById('marketSize').value) || 2800000000;

            // Calculate breakeven users
            const breakevenUsers = Math.ceil(monthlyCosts / pricePerUser);

            // Calculate revenue projections
            const year1Users = breakevenUsers * 1.5; // 50% growth beyond breakeven
            const year2Users = year1Users * 2; // Double users in year 2

            const year1Revenue = year1Users * pricePerUser * 12;
            const year2Revenue = year2Users * pricePerUser * 12;

            // Calculate market share
            const year2MarketValue = year2Revenue;
            const marketShare = ((year2MarketValue / marketSize) * 100).toFixed(2);

            // Update results display
            document.getElementById('breakevenUsers').textContent = breakevenUsers.toLocaleString();
            document.getElementById('year1Revenue').textContent = 'R' + year1Revenue.toLocaleString();
            document.getElementById('year2Revenue').textContent = 'R' + year2Revenue.toLocaleString();
            document.getElementById('marketShare').textContent = marketShare + '%';

            // Show results
            document.getElementById('configResults').style.display = 'block';
        },

        // Update business plan content
        updateBusinessPlan() {
            const productName = document.getElementById('productName').value || 'Lumerous';
            const productType = document.getElementById('productType').value;
            const targetMarket = document.getElementById('targetMarket').value;

            // Get descriptions
            const productDescription = this.productDescriptions[productType];
            const productCategory = this.productCategories[productType];
            const productTagline = this.productTaglines[productType];
            const marketDescription = this.marketDescriptions[targetMarket];

            // Get industry-specific content
            const industryData = this.industryContent[productType];

            // Update dynamic content
            this.updateDynamicContent('productName', productName);
            this.updateDynamicContent('productDescription', productDescription);
            this.updateDynamicContent('productCategory', productCategory);
            this.updateDynamicContent('productTagline', productTagline);
            this.updateDynamicContent('valueProposition', industryData.valueProposition);
            this.updateDynamicContent('usp', industryData.usp);
            this.updateDynamicContent('marketSize', industryData.marketSize);
            this.updateDynamicContent('targetSegment', industryData.targetSegment);
            this.updateDynamicContent('clientType', industryData.clientType);
            this.updateDynamicContent('serviceModel', industryData.serviceModel);
            this.updateDynamicContent('implementationTime', industryData.implementationTime);
            this.updateDynamicContent('successMetric', industryData.successMetric);

            // Update industry-specific lists
            this.updateIndustryLists(industryData);

            // Update financial figures throughout the document
            this.updateFinancialFigures();

            // Update navigation link
            this.updateNavigationLinks(productName);

            // Show success message
            this.showUpdateMessage();
        },

        // Update dynamic content elements
        updateDynamicContent(field, value) {
            const elements = document.querySelectorAll(`[data-field="${field}"]`);
            elements.forEach(element => {
                // Handle special cases for different field types
                if (field === 'productName' && element.classList.contains('service')) {
                    element.textContent = value + ' Platform';
                } else if (field === 'pricePerUser') {
                    element.textContent = 'R' + value + '/month';
                } else if (field === 'monthlyCosts') {
                    element.textContent = 'R' + parseInt(value).toLocaleString();
                } else if (field === 'initialCapital') {
                    element.textContent = 'R' + parseInt(value).toLocaleString();
                } else {
                    element.textContent = value;
                }

                element.classList.add('updated');
                setTimeout(() => element.classList.remove('updated'), 2000);
            });
        },

        // Update navigation links
        updateNavigationLinks(productName) {
            const navLink = document.querySelector('a[href="#lumerous"]');
            if (navLink) {
                navLink.textContent = productName + ' Platform';
            }
        },

        // Update industry-specific lists and content
        updateIndustryLists(industryData) {
            // Update competitors list
            this.updateListContent('competitors-list', industryData.competitors);

            // Update advantages list
            this.updateListContent('advantages-list', industryData.advantages);

            // Update threats list
            this.updateListContent('threats-list', industryData.threats);

            // Update opportunities list
            this.updateListContent('opportunities-list', industryData.opportunities);

            // Update key features list
            this.updateListContent('features-list', industryData.keyFeatures);
        },

        // Update list content helper function
        updateListContent(listId, items) {
            const listElement = document.getElementById(listId);
            if (listElement && items) {
                listElement.innerHTML = '';
                items.forEach(item => {
                    const li = document.createElement('li');
                    li.textContent = item;
                    li.classList.add('dynamic-content', 'updated');
                    listElement.appendChild(li);
                });

                // Remove updated class after animation
                setTimeout(() => {
                    listElement.querySelectorAll('.updated').forEach(el => {
                        el.classList.remove('updated');
                    });
                }, 2000);
            }
        },

        // Update financial figures throughout the document
        updateFinancialFigures() {
            const pricePerUser = parseFloat(document.getElementById('pricePerUser').value) || 200;
            const monthlyCosts = parseFloat(document.getElementById('monthlyCosts').value) || 300000;
            const initialCapital = parseFloat(document.getElementById('initialCapital').value) || 80000;

            // Update price displays
            const priceElements = document.querySelectorAll('[data-field="pricePerUser"]');
            priceElements.forEach(element => {
                element.textContent = 'R' + pricePerUser;
                element.classList.add('updated');
                setTimeout(() => element.classList.remove('updated'), 1000);
            });

            // Update cost displays
            const costElements = document.querySelectorAll('[data-field="monthlyCosts"]');
            costElements.forEach(element => {
                element.textContent = 'R' + monthlyCosts.toLocaleString();
                element.classList.add('updated');
                setTimeout(() => element.classList.remove('updated'), 1000);
            });

            // Update capital displays
            const capitalElements = document.querySelectorAll('[data-field="initialCapital"]');
            capitalElements.forEach(element => {
                element.textContent = 'R' + initialCapital.toLocaleString();
                element.classList.add('updated');
                setTimeout(() => element.classList.remove('updated'), 1000);
            });
        },

        // Reset to default values
        resetToDefaults() {
            document.getElementById('productName').value = 'Lumerous';
            document.getElementById('productType').value = 'education';
            document.getElementById('targetMarket').value = 'education';
            document.getElementById('pricePerUser').value = 200;
            document.getElementById('monthlyCosts').value = 300000;
            document.getElementById('initialCapital').value = 80000;
            document.getElementById('growthRate').value = 15;
            document.getElementById('marketSize').value = 2800000000;
            document.getElementById('breakevenMonth').value = 5;
            document.getElementById('developmentTime').value = 4;
            document.getElementById('launchDelay').value = 1;

            this.calculateMetrics();
            alert('✅ Configuration reset to default values!');
        },

        // Export to PDF
        exportToPDF() {
            const productName = document.getElementById('productName').value || 'Lumerous';
            const currentDate = new Date().toLocaleDateString();

            // Show loading message
            const loadingMessage = this.showLoadingMessage();

            // Prepare document for PDF export
            this.preparePDFDocument();

            // Set document title for PDF
            const originalTitle = document.title;
            document.title = `${productName} Business Plan - ${currentDate}`;

            // Trigger print dialog
            setTimeout(() => {
                window.print();

                // Restore original state after print
                setTimeout(() => {
                    document.title = originalTitle;
                    this.restorePDFDocument();
                    loadingMessage.remove();
                }, 1000);
            }, 500);
        },

        // Prepare document for PDF export
        preparePDFDocument() {
            // Add PDF export class to body for additional styling
            document.body.classList.add('pdf-export');

            // Temporarily hide configurator section
            const configurator = document.getElementById('configurator');
            if (configurator) {
                configurator.style.display = 'none';
            }

            // Add page break hints
            const sections = document.querySelectorAll('.section');
            sections.forEach((section, index) => {
                if (index > 0 && index % 2 === 0) {
                    section.style.pageBreakBefore = 'always';
                }
            });
        },

        // Restore document after PDF export
        restorePDFDocument() {
            // Remove PDF export class
            document.body.classList.remove('pdf-export');

            // Restore configurator section
            const configurator = document.getElementById('configurator');
            if (configurator) {
                configurator.style.display = 'block';
            }

            // Remove page break hints
            const sections = document.querySelectorAll('.section');
            sections.forEach(section => {
                section.style.pageBreakBefore = '';
            });
        },

        // Show loading message for PDF export
        showLoadingMessage() {
            const message = document.createElement('div');
            message.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: linear-gradient(135deg, #dc3545, #c82333);
                color: white;
                padding: 2rem 3rem;
                border-radius: 10px;
                box-shadow: 0 10px 25px rgba(0,0,0,0.3);
                z-index: 10001;
                font-weight: 600;
                font-size: 1.1rem;
                text-align: center;
                animation: pulse 1.5s infinite;
            `;
            message.innerHTML = `
                <div style="margin-bottom: 1rem; font-size: 2rem;">📄</div>
                <div>Preparing PDF Export...</div>
                <div style="font-size: 0.9rem; opacity: 0.9; margin-top: 0.5rem;">
                    Print dialog will open shortly
                </div>
            `;

            document.body.appendChild(message);
            return message;
        },

        // Export configuration
        exportConfiguration() {
            const config = {
                productName: document.getElementById('productName').value,
                productType: document.getElementById('productType').value,
                targetMarket: document.getElementById('targetMarket').value,
                pricePerUser: document.getElementById('pricePerUser').value,
                monthlyCosts: document.getElementById('monthlyCosts').value,
                initialCapital: document.getElementById('initialCapital').value,
                growthRate: document.getElementById('growthRate').value,
                marketSize: document.getElementById('marketSize').value,
                breakevenMonth: document.getElementById('breakevenMonth').value,
                developmentTime: document.getElementById('developmentTime').value,
                launchDelay: document.getElementById('launchDelay').value
            };

            const dataStr = JSON.stringify(config, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'business-plan-config.json';
            link.click();
            URL.revokeObjectURL(url);
        },

        // Show update message
        showUpdateMessage() {
            const productName = document.getElementById('productName').value || 'Lumerous';
            const updatedElements = document.querySelectorAll('.dynamic-content').length;

            const message = document.createElement('div');
            message.style.cssText = `
                position: fixed;
                top: 100px;
                right: 20px;
                background: linear-gradient(135deg, #28a745, #20c997);
                color: white;
                padding: 1.5rem 2rem;
                border-radius: 10px;
                box-shadow: 0 10px 25px rgba(0,0,0,0.2);
                z-index: 10000;
                font-weight: 600;
                font-size: 1rem;
                max-width: 300px;
                animation: slideIn 0.5s ease-out;
            `;
            message.innerHTML = `
                <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                    <span style="font-size: 1.2rem;">✅</span>
                    <strong>Business Plan Updated!</strong>
                </div>
                <div style="font-size: 0.9rem; opacity: 0.9;">
                    ${productName} configuration applied to ${updatedElements} elements
                </div>
            `;

            // Add slide-in animation
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
            `;
            document.head.appendChild(style);

            document.body.appendChild(message);

            setTimeout(() => {
                message.style.animation = 'slideIn 0.5s ease-out reverse';
                setTimeout(() => {
                    message.remove();
                    style.remove();
                }, 500);
            }, 3000);
        }
    };

    // Initialize configurator
    configurator.init();
	</script>

    <!-- Appendixes Section -->
    <section id="appendixes" class="section">
        <div class="container">
            <h2>Appendixes</h2>

            <!-- Branding and Brand Awareness Strategy -->
            <div class="appendix-section">
                <h3>Appendix A: Branding & Brand Awareness Strategy</h3>

                <div class="branding-overview">
                    <h4>Brand Positioning</h4>
                    <div class="brand-positioning">
                        <div class="positioning-item">
                            <h5>Brand Promise</h5>
                            <p>"Empowering South African learners with AI-driven education that adapts to every student's unique journey."</p>
                        </div>
                        <div class="positioning-item">
                            <h5>Brand Values</h5>
                            <ul>
                                <li><strong>Innovation:</strong> Cutting-edge AI technology for personalized learning</li>
                                <li><strong>Accessibility:</strong> Affordable, mobile-first solutions for all South Africans</li>
                                <li><strong>Excellence:</strong> 100% curriculum alignment with proven results</li>
                                <li><strong>Community:</strong> Building connections between students, teachers, and families</li>
                                <li><strong>Empowerment:</strong> Unlocking potential through adaptive learning</li>
                            </ul>
                        </div>
                        <div class="positioning-item">
                            <h5>Unique Value Proposition</h5>
                            <p>"The only AI-powered learning platform designed specifically for South African curricula, offering personalized tutoring in local languages at 40% less cost than competitors."</p>
                        </div>
                    </div>
                </div>

                <div class="brand-awareness-strategy">
                    <h4>Brand Awareness Implementation Strategy</h4>

                    <div class="awareness-phase">
                        <h5>Phase 1: Foundation Building (Months 1-3)</h5>
                        <div class="strategy-grid">
                            <div class="strategy-item">
                                <h6>Digital Presence</h6>
                                <ul>
                                    <li>Professional website with SEO optimization</li>
                                    <li>Social media presence (Facebook, Instagram, LinkedIn, TikTok)</li>
                                    <li>Google My Business optimization</li>
                                    <li>Educational blog content creation</li>
                                </ul>
                                <p><strong>Budget:</strong> R15,000/month | <strong>Timeline:</strong> 12 weeks</p>
                            </div>
                            <div class="strategy-item">
                                <h6>Content Marketing</h6>
                                <ul>
                                    <li>Weekly educational content (study tips, curriculum guides)</li>
                                    <li>Success stories and testimonials</li>
                                    <li>Video tutorials and platform demos</li>
                                    <li>Downloadable resources for teachers</li>
                                </ul>
                                <p><strong>Budget:</strong> R8,000/month | <strong>Timeline:</strong> Ongoing</p>
                            </div>
                        </div>
                    </div>

                    <div class="awareness-phase">
                        <h5>Phase 2: Market Penetration (Months 4-8)</h5>
                        <div class="strategy-grid">
                            <div class="strategy-item">
                                <h6>Educational Partnerships</h6>
                                <ul>
                                    <li>Teacher union collaborations</li>
                                    <li>School district pilot programs</li>
                                    <li>Education conference sponsorships</li>
                                    <li>University research partnerships</li>
                                </ul>
                                <p><strong>Budget:</strong> R25,000/month | <strong>Timeline:</strong> 20 weeks</p>
                            </div>
                            <div class="strategy-item">
                                <h6>Community Engagement</h6>
                                <ul>
                                    <li>Parent-teacher association presentations</li>
                                    <li>Community center workshops</li>
                                    <li>Local radio and newspaper features</li>
                                    <li>Student ambassador program</li>
                                </ul>
                                <p><strong>Budget:</strong> R12,000/month | <strong>Timeline:</strong> 20 weeks</p>
                            </div>
                        </div>
                    </div>

                    <div class="awareness-phase">
                        <h5>Phase 3: Scale & Expansion (Months 9-12)</h5>
                        <div class="strategy-grid">
                            <div class="strategy-item">
                                <h6>Digital Advertising</h6>
                                <ul>
                                    <li>Google Ads targeting education keywords</li>
                                    <li>Facebook/Instagram targeted campaigns</li>
                                    <li>YouTube educational content promotion</li>
                                    <li>Influencer partnerships with educators</li>
                                </ul>
                                <p><strong>Budget:</strong> R35,000/month | <strong>Timeline:</strong> 16 weeks</p>
                            </div>
                            <div class="strategy-item">
                                <h6>Public Relations</h6>
                                <ul>
                                    <li>Press releases for major milestones</li>
                                    <li>Industry award submissions</li>
                                    <li>Thought leadership articles</li>
                                    <li>Podcast guest appearances</li>
                                </ul>
                                <p><strong>Budget:</strong> R10,000/month | <strong>Timeline:</strong> 16 weeks</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="brand-metrics">
                    <h4>Brand Awareness KPIs & Targets</h4>
                    <div class="metrics-grid">
                        <div class="metric-item">
                            <h5>Digital Metrics</h5>
                            <ul>
                                <li>Website traffic: 10,000 monthly visitors by Month 6</li>
                                <li>Social media followers: 5,000 across platforms by Month 8</li>
                                <li>Email subscribers: 2,000 by Month 10</li>
                                <li>Brand mention tracking: 50 mentions/month by Month 12</li>
                            </ul>
                        </div>
                        <div class="metric-item">
                            <h5>Engagement Metrics</h5>
                            <ul>
                                <li>Content engagement rate: 5% average</li>
                                <li>Video completion rate: 70%</li>
                                <li>Email open rate: 25%</li>
                                <li>Social media engagement: 3% average</li>
                            </ul>
                        </div>
                        <div class="metric-item">
                            <h5>Conversion Metrics</h5>
                            <ul>
                                <li>Website to trial conversion: 8%</li>
                                <li>Trial to paid conversion: 25%</li>
                                <li>Referral rate: 15%</li>
                                <li>Customer acquisition cost: R150</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Client Approach Plan for Lumerous -->
            <div class="appendix-section">
                <h3>Appendix B: Client Approach Plan - Lumerous Education Platform</h3>

                <div class="client-approach-overview">
                    <h4>Target Client Segments</h4>
                    <div class="client-segments">
                        <div class="segment-card">
                            <h5>Primary: Individual Students & Parents</h5>
                            <p><strong>Target:</strong> 50,000 users | <strong>Revenue Potential:</strong> R10M/month</p>
                        </div>
                        <div class="segment-card">
                            <h5>Secondary: Schools & Institutions</h5>
                            <p><strong>Target:</strong> 500 schools | <strong>Revenue Potential:</strong> R2.5M/month</p>
                        </div>
                        <div class="segment-card">
                            <h5>Tertiary: Tutoring Centers & NGOs</h5>
                            <p><strong>Target:</strong> 200 centers | <strong>Revenue Potential:</strong> R600K/month</p>
                        </div>
                    </div>
                </div>

                <div class="approach-methodology">
                    <h4>Client Acquisition Methodology</h4>

                    <div class="approach-phase">
                        <h5>Phase 1: Discovery & Qualification (Week 1-2)</h5>
                        <div class="phase-content">
                            <div class="steps-column">
                                <h6>Steps to Take</h6>
                                <ol>
                                    <li>Initial contact through referral or inbound inquiry</li>
                                    <li>Conduct needs assessment questionnaire</li>
                                    <li>Schedule discovery call with decision makers</li>
                                    <li>Analyze current learning challenges and pain points</li>
                                    <li>Qualify budget, timeline, and decision-making process</li>
                                    <li>Identify key stakeholders and influencers</li>
                                </ol>
                            </div>
                            <div class="deliverables-column">
                                <h6>Client Deliverables</h6>
                                <ul>
                                    <li>Comprehensive needs assessment report</li>
                                    <li>Customized solution overview presentation</li>
                                    <li>ROI projection specific to their context</li>
                                    <li>Implementation timeline proposal</li>
                                </ul>
                            </div>
                        </div>
                        <p><strong>Timeline:</strong> 2 weeks | <strong>Success Metric:</strong> 80% qualification rate</p>
                    </div>

                    <div class="approach-phase">
                        <h5>Phase 2: Solution Design & Proposal (Week 3-4)</h5>
                        <div class="phase-content">
                            <div class="steps-column">
                                <h6>Steps to Take</h6>
                                <ol>
                                    <li>Design customized Lumerous implementation plan</li>
                                    <li>Create detailed curriculum alignment mapping</li>
                                    <li>Develop user onboarding and training strategy</li>
                                    <li>Prepare technical integration requirements</li>
                                    <li>Calculate pricing based on user volume and features</li>
                                    <li>Schedule formal proposal presentation</li>
                                </ol>
                            </div>
                            <div class="deliverables-column">
                                <h6>Client Deliverables</h6>
                                <ul>
                                    <li>Detailed implementation proposal</li>
                                    <li>Curriculum alignment documentation</li>
                                    <li>User training and support plan</li>
                                    <li>Technical requirements specification</li>
                                    <li>Pricing proposal with payment options</li>
                                    <li>Success metrics and KPI framework</li>
                                </ul>
                            </div>
                        </div>
                        <p><strong>Timeline:</strong> 2 weeks | <strong>Success Metric:</strong> 60% proposal acceptance rate</p>
                    </div>

                    <div class="approach-phase">
                        <h5>Phase 3: Pilot Program & Validation (Week 5-8)</h5>
                        <div class="phase-content">
                            <div class="steps-column">
                                <h6>Steps to Take</h6>
                                <ol>
                                    <li>Set up limited pilot with 50-100 users</li>
                                    <li>Provide intensive onboarding and training</li>
                                    <li>Monitor usage patterns and engagement metrics</li>
                                    <li>Collect feedback through surveys and interviews</li>
                                    <li>Demonstrate measurable learning improvements</li>
                                    <li>Present pilot results and expansion proposal</li>
                                </ol>
                            </div>
                            <div class="deliverables-column">
                                <h6>Client Deliverables</h6>
                                <ul>
                                    <li>Fully configured pilot platform access</li>
                                    <li>Comprehensive user training sessions</li>
                                    <li>Weekly progress reports and analytics</li>
                                    <li>Pilot results summary with recommendations</li>
                                    <li>Full implementation proposal</li>
                                </ul>
                            </div>
                        </div>
                        <p><strong>Timeline:</strong> 4 weeks | <strong>Success Metric:</strong> 85% pilot satisfaction, 40% conversion to full contract</p>
                    </div>

                    <div class="approach-phase">
                        <h5>Phase 4: Full Implementation & Onboarding (Week 9-16)</h5>
                        <div class="phase-content">
                            <div class="steps-column">
                                <h6>Steps to Take</h6>
                                <ol>
                                    <li>Execute full platform deployment</li>
                                    <li>Conduct comprehensive user training program</li>
                                    <li>Implement data migration and integration</li>
                                    <li>Establish ongoing support processes</li>
                                    <li>Monitor adoption rates and user engagement</li>
                                    <li>Optimize based on usage patterns and feedback</li>
                                </ol>
                            </div>
                            <div class="deliverables-column">
                                <h6>Client Deliverables</h6>
                                <ul>
                                    <li>Fully deployed Lumerous platform</li>
                                    <li>Complete user training and certification</li>
                                    <li>Data migration and system integration</li>
                                    <li>24/7 technical support access</li>
                                    <li>Monthly performance reports</li>
                                    <li>Ongoing optimization recommendations</li>
                                </ul>
                            </div>
                        </div>
                        <p><strong>Timeline:</strong> 8 weeks | <strong>Success Metric:</strong> 90% user adoption, 95% uptime</p>
                    </div>

                    <div class="approach-phase">
                        <h5>Phase 5: Growth & Expansion (Month 5+)</h5>
                        <div class="phase-content">
                            <div class="steps-column">
                                <h6>Steps to Take</h6>
                                <ol>
                                    <li>Analyze usage data and learning outcomes</li>
                                    <li>Identify opportunities for feature expansion</li>
                                    <li>Propose additional user licenses or modules</li>
                                    <li>Facilitate referrals to other institutions</li>
                                    <li>Conduct quarterly business reviews</li>
                                    <li>Plan renewal and contract expansion</li>
                                </ol>
                            </div>
                            <div class="deliverables-column">
                                <h6>Client Deliverables</h6>
                                <ul>
                                    <li>Quarterly performance analytics reports</li>
                                    <li>Feature enhancement recommendations</li>
                                    <li>Expansion proposals and pricing</li>
                                    <li>Referral program benefits</li>
                                    <li>Contract renewal proposals</li>
                                </ul>
                            </div>
                        </div>
                        <p><strong>Timeline:</strong> Ongoing | <strong>Success Metric:</strong> 95% retention rate, 30% expansion revenue</p>
                    </div>
                </div>

                <div class="success-framework">
                    <h4>Success Metrics & KPIs</h4>
                    <div class="metrics-grid">
                        <div class="metric-category">
                            <h5>Sales Performance</h5>
                            <ul>
                                <li>Lead to qualified prospect: 25%</li>
                                <li>Qualified prospect to pilot: 60%</li>
                                <li>Pilot to full contract: 40%</li>
                                <li>Average deal size: R120,000 annually</li>
                                <li>Sales cycle length: 16 weeks average</li>
                            </ul>
                        </div>
                        <div class="metric-category">
                            <h5>Client Success</h5>
                            <ul>
                                <li>User adoption rate: 90% within 30 days</li>
                                <li>Learning improvement: 25% average increase</li>
                                <li>Client satisfaction: 4.5/5 rating</li>
                                <li>Support ticket resolution: 24-hour average</li>
                                <li>Platform uptime: 99.5% minimum</li>
                            </ul>
                        </div>
                        <div class="metric-category">
                            <h5>Growth Indicators</h5>
                            <ul>
                                <li>Monthly recurring revenue growth: 15%</li>
                                <li>Customer lifetime value: R500,000</li>
                                <li>Referral rate: 25% of new clients</li>
                                <li>Contract renewal rate: 95%</li>
                                <li>Expansion revenue: 30% of existing clients</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Client Approach Plans for Other Services -->
            <div class="appendix-section">
                <h3>Appendix C: Client Approach Plans - Additional Services</h3>

                <!-- POPIA Compliance SaaS -->
                <div class="service-approach">
                    <h4>POPIA Compliance SaaS</h4>
                    <div class="service-overview">
                        <p><strong>Target Market:</strong> South African businesses, legal firms, healthcare providers</p>
                        <p><strong>Pricing:</strong> R2,000-5,000/month | <strong>Implementation:</strong> 4-6 weeks</p>
                    </div>

                    <div class="approach-timeline">
                        <div class="timeline-phase">
                            <h5>Week 1-2: Compliance Assessment</h5>
                            <div class="phase-details">
                                <div class="steps">
                                    <strong>Steps:</strong>
                                    <ol>
                                        <li>Conduct comprehensive data audit</li>
                                        <li>Identify POPIA compliance gaps</li>
                                        <li>Map current data processing activities</li>
                                        <li>Assess risk levels and priorities</li>
                                    </ol>
                                </div>
                                <div class="deliverables">
                                    <strong>Deliverables:</strong>
                                    <ul>
                                        <li>POPIA compliance gap analysis report</li>
                                        <li>Data processing activity mapping</li>
                                        <li>Risk assessment matrix</li>
                                        <li>Compliance roadmap proposal</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="timeline-phase">
                            <h5>Week 3-4: System Configuration</h5>
                            <div class="phase-details">
                                <div class="steps">
                                    <strong>Steps:</strong>
                                    <ol>
                                        <li>Configure automated monitoring systems</li>
                                        <li>Set up data classification protocols</li>
                                        <li>Implement consent management tools</li>
                                        <li>Create compliance reporting dashboards</li>
                                    </ol>
                                </div>
                                <div class="deliverables">
                                    <strong>Deliverables:</strong>
                                    <ul>
                                        <li>Fully configured compliance platform</li>
                                        <li>Automated monitoring and alerts</li>
                                        <li>Data subject request management system</li>
                                        <li>Compliance training materials</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="timeline-phase">
                            <h5>Week 5-6: Training & Go-Live</h5>
                            <div class="phase-details">
                                <div class="steps">
                                    <strong>Steps:</strong>
                                    <ol>
                                        <li>Train compliance officers and staff</li>
                                        <li>Conduct system testing and validation</li>
                                        <li>Go live with full monitoring</li>
                                        <li>Establish ongoing support processes</li>
                                    </ol>
                                </div>
                                <div class="deliverables">
                                    <strong>Deliverables:</strong>
                                    <ul>
                                        <li>Staff training certification</li>
                                        <li>System validation reports</li>
                                        <li>24/7 compliance monitoring</li>
                                        <li>Monthly compliance reports</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- E-commerce Solutions -->
                <div class="service-approach">
                    <h4>E-commerce Solutions</h4>
                    <div class="service-overview">
                        <p><strong>Target Market:</strong> SMEs, retailers, entrepreneurs</p>
                        <p><strong>Pricing:</strong> R50,000 setup + R2,000/month | <strong>Implementation:</strong> 6-8 weeks</p>
                    </div>

                    <div class="approach-timeline">
                        <div class="timeline-phase">
                            <h5>Week 1-2: Discovery & Design</h5>
                            <div class="phase-details">
                                <div class="steps">
                                    <strong>Steps:</strong>
                                    <ol>
                                        <li>Analyze business requirements and goals</li>
                                        <li>Design custom e-commerce architecture</li>
                                        <li>Create user experience wireframes</li>
                                        <li>Plan payment gateway integrations</li>
                                    </ol>
                                </div>
                                <div class="deliverables">
                                    <strong>Deliverables:</strong>
                                    <ul>
                                        <li>Technical specification document</li>
                                        <li>UI/UX design mockups</li>
                                        <li>Project timeline and milestones</li>
                                        <li>Integration requirements analysis</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="timeline-phase">
                            <h5>Week 3-5: Development & Integration</h5>
                            <div class="phase-details">
                                <div class="steps">
                                    <strong>Steps:</strong>
                                    <ol>
                                        <li>Develop custom e-commerce platform</li>
                                        <li>Integrate payment gateways and APIs</li>
                                        <li>Implement inventory management system</li>
                                        <li>Set up analytics and reporting tools</li>
                                    </ol>
                                </div>
                                <div class="deliverables">
                                    <strong>Deliverables:</strong>
                                    <ul>
                                        <li>Fully functional e-commerce website</li>
                                        <li>Payment processing integration</li>
                                        <li>Inventory management dashboard</li>
                                        <li>Analytics and reporting system</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="timeline-phase">
                            <h5>Week 6-8: Testing & Launch</h5>
                            <div class="phase-details">
                                <div class="steps">
                                    <strong>Steps:</strong>
                                    <ol>
                                        <li>Conduct comprehensive testing</li>
                                        <li>Train client on platform management</li>
                                        <li>Launch website and marketing campaigns</li>
                                        <li>Monitor performance and optimize</li>
                                    </ol>
                                </div>
                                <div class="deliverables">
                                    <strong>Deliverables:</strong>
                                    <ul>
                                        <li>Live e-commerce website</li>
                                        <li>Admin training and documentation</li>
                                        <li>SEO optimization and marketing setup</li>
                                        <li>Ongoing maintenance and support plan</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- IT Services -->
                <div class="service-approach">
                    <h4>IT Services & Infrastructure</h4>
                    <div class="service-overview">
                        <p><strong>Target Market:</strong> Small to medium businesses</p>
                        <p><strong>Pricing:</strong> R15,000-50,000 setup + R5,000/month support | <strong>Implementation:</strong> 2-4 weeks</p>
                    </div>

                    <div class="approach-timeline">
                        <div class="timeline-phase">
                            <h5>Week 1: Infrastructure Assessment</h5>
                            <div class="phase-details">
                                <div class="steps">
                                    <strong>Steps:</strong>
                                    <ol>
                                        <li>Conduct comprehensive IT audit</li>
                                        <li>Assess current hardware and software</li>
                                        <li>Identify security vulnerabilities</li>
                                        <li>Plan infrastructure improvements</li>
                                    </ol>
                                </div>
                                <div class="deliverables">
                                    <strong>Deliverables:</strong>
                                    <ul>
                                        <li>IT infrastructure audit report</li>
                                        <li>Security assessment findings</li>
                                        <li>Improvement recommendations</li>
                                        <li>Implementation proposal and timeline</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="timeline-phase">
                            <h5>Week 2-3: Implementation</h5>
                            <div class="phase-details">
                                <div class="steps">
                                    <strong>Steps:</strong>
                                    <ol>
                                        <li>Install and configure hardware</li>
                                        <li>Set up networking and security systems</li>
                                        <li>Migrate data and applications</li>
                                        <li>Implement backup and disaster recovery</li>
                                    </ol>
                                </div>
                                <div class="deliverables">
                                    <strong>Deliverables:</strong>
                                    <ul>
                                        <li>Fully configured IT infrastructure</li>
                                        <li>Network security implementation</li>
                                        <li>Data migration and backup systems</li>
                                        <li>Disaster recovery procedures</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="timeline-phase">
                            <h5>Week 4: Training & Support Setup</h5>
                            <div class="phase-details">
                                <div class="steps">
                                    <strong>Steps:</strong>
                                    <ol>
                                        <li>Train staff on new systems</li>
                                        <li>Document all procedures and processes</li>
                                        <li>Set up remote monitoring and support</li>
                                        <li>Establish maintenance schedules</li>
                                    </ol>
                                </div>
                                <div class="deliverables">
                                    <strong>Deliverables:</strong>
                                    <ul>
                                        <li>Staff training and certification</li>
                                        <li>Complete system documentation</li>
                                        <li>24/7 remote monitoring setup</li>
                                        <li>Ongoing maintenance contract</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="service-success-metrics">
                    <h4>Success Metrics Across All Services</h4>
                    <div class="metrics-comparison">
                        <table class="success-table">
                            <thead>
                                <tr>
                                    <th>Service</th>
                                    <th>Implementation Time</th>
                                    <th>Client Satisfaction Target</th>
                                    <th>Success Rate</th>
                                    <th>Retention Rate</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Lumerous Education</td>
                                    <td>16 weeks</td>
                                    <td>4.5/5</td>
                                    <td>90% user adoption</td>
                                    <td>95%</td>
                                </tr>
                                <tr>
                                    <td>POPIA Compliance</td>
                                    <td>6 weeks</td>
                                    <td>4.7/5</td>
                                    <td>100% compliance achievement</td>
                                    <td>98%</td>
                                </tr>
                                <tr>
                                    <td>E-commerce Solutions</td>
                                    <td>8 weeks</td>
                                    <td>4.6/5</td>
                                    <td>25% revenue increase</td>
                                    <td>92%</td>
                                </tr>
                                <tr>
                                    <td>IT Services</td>
                                    <td>4 weeks</td>
                                    <td>4.8/5</td>
                                    <td>99.5% uptime</td>
                                    <td>96%</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </section>

    </main> <!-- Close main-content -->
</body>
</html>