<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Solving Tomorrow - Product Roadmap</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
            background-color: #f5f7fa;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        h1 {
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        .timeline {
            position: relative;
            max-width: 1200px;
            margin: 0 auto;
        }
        .timeline::after {
            content: '';
            position: absolute;
            width: 6px;
            background-color: #3498db;
            top: 0;
            bottom: 0;
            left: 50%;
            margin-left: -3px;
        }
        .phase {
            padding: 10px 40px;
            position: relative;
            background-color: inherit;
            width: 50%;
        }
        .phase::after {
            content: '';
            position: absolute;
            width: 25px;
            height: 25px;
            right: -17px;
            background-color: white;
            border: 4px solid #3498db;
            top: 15px;
            border-radius: 50%;
            z-index: 1;
        }
        .left {
            left: 0;
        }
        .right {
            left: 50%;
        }
        .left::before {
            content: " ";
            height: 0;
            position: absolute;
            top: 22px;
            width: 0;
            z-index: 1;
            right: 30px;
            border: medium solid white;
            border-width: 10px 0 10px 10px;
            border-color: transparent transparent transparent white;
        }
        .right::before {
            content: " ";
            height: 0;
            position: absolute;
            top: 22px;
            width: 0;
            z-index: 1;
            left: 30px;
            border: medium solid white;
            border-width: 10px 10px 10px 0;
            border-color: transparent white transparent transparent;
        }
        .right::after {
            left: -16px;
        }
        .phase-content {
            padding: 20px 30px;
            background-color: white;
            position: relative;
            border-radius: 6px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .phase-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .phase-title {
            font-weight: bold;
            color: #3498db;
            font-size: 18px;
        }
        .phase-date {
            background-color: #3498db;
            color: white;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 14px;
        }
        .milestone-list {
            list-style-type: none;
            padding-left: 0;
        }
        .milestone-item {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
        }
        .milestone-item:before {
            content: "•";
            color: #3498db;
            font-weight: bold;
            display: inline-block;
            width: 1em;
            margin-left: -1em;
        }
        .status-checkbox {
            margin-left: auto;
            width: 20px;
            height: 20px;
        }
        .metrics-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .metric-card {
            background-color: #e8f4fc;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #3498db;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .feature-card {
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .feature-card.completed {
            border-left: 4px solid #2ecc71;
        }
        .feature-card.in-progress {
            border-left: 4px solid #f39c12;
        }
        .feature-card.planned {
            border-left: 4px solid #3498db;
        }
        .feature-status {
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 3px;
            color: white;
            display: inline-block;
            margin-bottom: 8px;
        }
        .status-completed {
            background-color: #2ecc71;
        }
        .status-in-progress {
            background-color: #f39c12;
        }
        .status-planned {
            background-color: #3498db;
        }
        @media screen and (max-width: 600px) {
            .timeline::after {
                left: 31px;
            }
            .phase {
                width: 100%;
                padding-left: 70px;
                padding-right: 25px;
            }
            .phase::before {
                left: 60px;
                border: medium solid white;
                border-width: 10px 10px 10px 0;
                border-color: transparent white transparent transparent;
            }
            .left::after, .right::after {
                left: 15px;
            }
            .right {
                left: 0%;
            }
        }
    </style>
</head>
<body>
    <h1>Lumerous Platform Development Roadmap</h1>
    
    <div class="timeline">
        <div class="phase left">
            <div class="phase-content">
                <div class="phase-header">
                    <div class="phase-title">Phase 1: Foundation & Launch</div>
                    <div class="phase-date">Jan-Jun 2025</div>
                </div>
                <p>MVP development and initial market entry with R80,000 initial capital</p>
                <ul class="milestone-list">
                    <li class="milestone-item">
                        <span>Complete POPIA compliance and BEE certification</span>
                        <input type="checkbox" class="status-checkbox" checked>
                    </li>
                    <li class="milestone-item">
                        <span>Finalize MVP development with core features</span>
                        <input type="checkbox" class="status-checkbox" checked>
                    </li>
                    <li class="milestone-item">
                        <span>Launch pilot program in first school</span>
                        <input type="checkbox" class="status-checkbox" checked>
                    </li>
                    <li class="milestone-item">
                        <span>Expand to 3 pilot schools, gather feedback</span>
                        <input type="checkbox">
                    </li>
                    <li class="milestone-item">
                        <span>Implement feedback, prepare for public launch</span>
                        <input type="checkbox">
                    </li>
                    <li class="milestone-item">
                        <span>Achieve breakeven: 1,500 users, R300K monthly revenue</span>
                        <input type="checkbox">
                    </li>
                </ul>
                <div class="metrics-container">
                    <div class="metric-card">
                        <div class="metric-value">1,500</div>
                        <div>PAYING USERS</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">3</div>
                        <div>PILOT CLIENTS</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">R300K</div>
                        <div>MONTHLY REVENUE</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">95%</div>
                        <div>USER SATISFACTION</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="phase right">
            <div class="phase-content">
                <div class="phase-header">
                    <div class="phase-title">Phase 2: Market Penetration</div>
                    <div class="phase-date">Jul-Dec 2025</div>
                </div>
                <p>Scale beyond breakeven and implement profit allocation strategy</p>
                <ul class="milestone-list">
                    <li class="milestone-item">
                        <span>Scale to 2,000 users, launch additional services</span>
                        <input type="checkbox">
                    </li>
                    <li class="milestone-item">
                        <span>Implement profit allocation, expand to 10 organizations</span>
                        <input type="checkbox">
                    </li>
                    <li class="milestone-item">
                        <span>Reach 2,500 users, expand service portfolio</span>
                        <input type="checkbox">
                    </li>
                    <li class="milestone-item">
                        <span>Launch referral program</span>
                        <input type="checkbox">
                    </li>
                    <li class="milestone-item">
                        <span>Reach 3,000+ users, expand to 15 client partnerships</span>
                        <input type="checkbox">
                    </li>
                </ul>
                <div class="metrics-container">
                    <div class="metric-card">
                        <div class="metric-value">3,000</div>
                        <div>ACTIVE USERS</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">15</div>
                        <div>CLIENT PARTNERS</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">R600K</div>
                        <div>MONTHLY REVENUE</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">R180K</div>
                        <div>MONTHLY PROFIT</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="phase left">
            <div class="phase-content">
                <div class="phase-header">
                    <div class="phase-title">Phase 3: Rapid Expansion</div>
                    <div class="phase-date">2026</div>
                </div>
                <p>Scale to 5,000+ users across 3 provinces and establish market leadership</p>
                <ul class="milestone-list">
                    <li class="milestone-item">
                        <span>Launch in Western Cape and Eastern Cape</span>
                        <input type="checkbox">
                    </li>
                    <li class="milestone-item">
                        <span>Reach 3,000 users, launch premium tier</span>
                        <input type="checkbox">
                    </li>
                    <li class="milestone-item">
                        <span>50 school partnerships, enterprise features</span>
                        <input type="checkbox">
                    </li>
                    <li class="milestone-item">
                        <span>5,000+ users, market leadership established</span>
                        <input type="checkbox">
                    </li>
                </ul>
                <div class="metrics-container">
                    <div class="metric-card">
                        <div class="metric-value">5,000+</div>
                        <div>ACTIVE USERS</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">50</div>
                        <div>SCHOOL PARTNERS</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">R1M</div>
                        <div>MONTHLY REVENUE</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">30%</div>
                        <div>MARKET SHARE</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="phase right">
            <div class="phase-content">
                <div class="phase-header">
                    <div class="phase-title">Phase 4: Market Leadership & Expansion</div>
                    <div class="phase-date">2027+</div>
                </div>
                <p>Achieve market dominance and prepare for strategic exit opportunities</p>
                <ul class="milestone-list">
                    <li class="milestone-item">
                        <span>Launch in Botswana and Namibia</span>
                        <input type="checkbox">
                    </li>
                    <li class="milestone-item">
                        <span>10,000+ users, advanced AI features</span>
                        <input type="checkbox">
                    </li>
                    <li class="milestone-item">
                        <span>Strategic partnerships with publishers</span>
                        <input type="checkbox">
                    </li>
                    <li class="milestone-item">
                        <span>Evaluate exit opportunities (IPO/Acquisition)</span>
                        <input type="checkbox">
                    </li>
                </ul>
                <div class="metrics-container">
                    <div class="metric-card">
                        <div class="metric-value">10,000+</div>
                        <div>ACTIVE USERS</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">3</div>
                        <div>COUNTRIES</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">R2M+</div>
                        <div>MONTHLY REVENUE</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">50%</div>
                        <div>MARKET SHARE</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <h2>Platform Features Development</h2>
    <div class="feature-grid">
        <div class="feature-card completed">
            <span class="feature-status status-completed">Completed</span>
            <h3>AI-Powered Tutoring</h3>
            <p>Advanced machine learning algorithms provide personalized learning paths</p>
        </div>
        <div class="feature-card completed">
            <span class="feature-status status-completed">Completed</span>
            <h3>Curriculum Alignment</h3>
            <p>100% alignment with CAPS and IEB curricula</p>
        </div>
        <div class="feature-card in-progress">
            <span class="feature-status status-in-progress">In Progress</span>
            <h3>Progress Tracking</h3>
            <p>Detailed analytics and reporting for students and teachers</p>
        </div>
        <div class="feature-card in-progress">
            <span class="feature-status status-in-progress">In Progress</span>
            <h3>Gamified Learning</h3>
            <p>Engagement through badges, leaderboards, and rewards</p>
        </div>
        <div class="feature-card planned">
            <span class="feature-status status-planned">Planned</span>
            <h3>Offline Mode</h3>
            <p>Functionality for areas with limited internet connectivity</p>
        </div>
        <div class="feature-card planned">
            <span class="feature-status status-planned">Planned</span>
            <h3>Teacher Collaboration</h3>
            <p>Tools for resource sharing and professional development</p>
        </div>
        <div class="feature-card planned">
            <span class="feature-status status-planned">Planned</span>
            <h3>Parent Dashboard</h3>
            <p>Monitoring and reporting for parents/guardians</p>
        </div>
        <div class="feature-card planned">
            <span class="feature-status status-planned">Planned</span>
            <h3>Multilingual Support</h3>
            <p>Additional South African language options</p>
        </div>
    </div>
    
    <script src="product_roadmap.js"></script>
</body>
</html>