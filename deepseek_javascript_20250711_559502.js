document.addEventListener('DOMContentLoaded', function() {
    // Initialize revenue chart
    const ctx = document.getElementById('revenueChart').getContext('2d');
    const revenueChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['Year 1', 'Year 2', 'Year 3'],
            datasets: [
                {
                    label: 'Lumerous Revenue',
                    data: [3600000, 7200000, 14400000],
                    backgroundColor: '#3498db',
                },
                {
                    label: 'Additional Services',
                    data: [1200000, 2400000, 4800000],
                    backgroundColor: '#2ecc71',
                }
            ]
        },
        options: {
            responsive: true,
            scales: {
                x: {
                    stacked: true,
                },
                y: {
                    stacked: true,
                    ticks: {
                        callback: function(value) {
                            return 'R' + (value / 1000000) + 'M';
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            label += 'R' + context.raw.toLocaleString();
                            return label;
                        }
                    }
                }
            }
        }
    });

    // Update range value displays
    document.getElementById('premiumRate').addEventListener('input', function() {
        document.getElementById('premiumRateValue').textContent = this.value + '%';
        calculateProjection();
    });
    
    document.getElementById('churnRate').addEventListener('input', function() {
        document.getElementById('churnRateValue').textContent = this.value + '%';
        calculateProjection();
    });

    // Calculate projection when inputs change
    document.getElementById('newUsers').addEventListener('change', calculateProjection);
    document.getElementById('newSchools').addEventListener('change', calculateProjection);

    // Initial calculation
    calculateProjection();

    function calculateProjection() {
        const newUsers = parseInt(document.getElementById('newUsers').value) || 0;
        const premiumRate = parseInt(document.getElementById('premiumRate').value) || 0;
        const newSchools = parseInt(document.getElementById('newSchools').value) || 0;
        const churnRate = parseInt(document.getElementById('churnRate').value) || 0;
        
        // Simplified calculation for demo purposes
        const baseRevenue = newUsers * 200;
        const premiumRevenue = baseRevenue * (premiumRate / 100) * 50; // R50 premium per user
        const schoolRevenue = newSchools * 5000;
        const churnAdjustedRevenue = (baseRevenue + premiumRevenue + schoolRevenue) * (1 - (churnRate / 100));
        
        document.getElementById('projectedRevenue').textContent = 'R' + Math.round(churnAdjustedRevenue).toLocaleString();
        
        // Determine breakeven point
        const breakevenUsers = 1500;
        const monthsToBreakeven = Math.ceil(breakevenUsers / newUsers);
        document.getElementById('breakevenPoint').textContent = monthsToBreakeven <= 12 ? 
            'Month ' + monthsToBreakeven : 
            'Beyond 12 months at current rate';
    }
});