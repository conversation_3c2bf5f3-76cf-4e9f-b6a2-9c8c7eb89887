document.addEventListener('DOMContentLoaded', function() {
    // Checkbox functionality for milestones
    const checkboxes = document.querySelectorAll('.status-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const milestoneItem = this.closest('.milestone-item');
            if (this.checked) {
                milestoneItem.style.textDecoration = 'line-through';
                milestoneItem.style.opacity = '0.7';
            } else {
                milestoneItem.style.textDecoration = 'none';
                milestoneItem.style.opacity = '1';
            }
        });
        
        // Initialize completed items
        if (checkbox.checked) {
            const milestoneItem = checkbox.closest('.milestone-item');
            milestoneItem.style.textDecoration = 'line-through';
            milestoneItem.style.opacity = '0.7';
        }
    });

    // Animate timeline phases on scroll
    const phases = document.querySelectorAll('.phase');
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateX(0)';
            }
        });
    }, { threshold: 0.1 });

    phases.forEach(phase => {
        phase.style.opacity = '0';
        if (phase.classList.contains('left')) {
            phase.style.transform = 'translateX(-50px)';
        } else {
            phase.style.transform = 'translateX(50px)';
        }
        phase.style.transition = 'all 0.5s ease-out';
        observer.observe(phase);
    });
});