<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Roadmap | Solving Tomorrow</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #FF6B35;
            --primary-dark: #E05A2B;
            --secondary: #2D2D2D;
            --light: #F7F7F7;
            --dark: #1A1A1A;
            --grey: #6C757D;
            --light-grey: #E9ECEF;
            --blue: #3498db;
            --green: #2ecc71;
            --orange: #f39c12;
            --red: #e74c3c;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            color: var(--dark);
            background-color: var(--light);
        }

        /* Navigation */
        .nav-header {
            background-color: var(--dark);
            color: var(--light);
            padding: 1rem 2rem;
            margin: -20px -20px 30px -20px;
            border-radius: 0 0 10px 10px;
        }

        .nav-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1160px;
            margin: 0 auto;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary);
            text-decoration: none;
        }

        .back-btn {
            background-color: var(--primary);
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            font-weight: 500;
            transition: background-color 0.3s ease;
        }

        .back-btn:hover {
            background-color: var(--primary-dark);
        }

        h1, h2, h3 {
            color: var(--secondary);
        }

        h1 {
            border-bottom: 3px solid var(--primary);
            padding-bottom: 10px;
            margin-bottom: 30px;
            font-size: 2.5rem;
            font-weight: 700;
        }

        .timeline {
            position: relative;
            max-width: 1200px;
            margin: 0 auto;
        }

        .timeline::after {
            content: '';
            position: absolute;
            width: 6px;
            background: linear-gradient(to bottom, var(--primary), var(--primary-dark));
            top: 0;
            bottom: 0;
            left: 50%;
            margin-left: -3px;
            border-radius: 3px;
        }

        .phase {
            padding: 10px 40px;
            position: relative;
            background-color: inherit;
            width: 50%;
        }

        .phase::after {
            content: '';
            position: absolute;
            width: 25px;
            height: 25px;
            right: -17px;
            background-color: white;
            border: 4px solid var(--primary);
            top: 15px;
            border-radius: 50%;
            z-index: 1;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .left {
            left: 0;
        }

        .right {
            left: 50%;
        }

        .left::before {
            content: " ";
            height: 0;
            position: absolute;
            top: 22px;
            width: 0;
            z-index: 1;
            right: 30px;
            border: medium solid white;
            border-width: 10px 0 10px 10px;
            border-color: transparent transparent transparent white;
        }

        .right::before {
            content: " ";
            height: 0;
            position: absolute;
            top: 22px;
            width: 0;
            z-index: 1;
            left: 30px;
            border: medium solid white;
            border-width: 10px 10px 10px 0;
            border-color: transparent white transparent transparent;
        }

        .right::after {
            left: -16px;
        }

        .phase-content {
            padding: 25px;
            background-color: white;
            position: relative;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: 1px solid var(--light-grey);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .phase-content:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .phase-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            flex-wrap: wrap;
            gap: 10px;
        }

        .phase-title {
            font-weight: 700;
            color: var(--primary);
            font-size: 1.3rem;
        }

        .phase-date {
            background-color: var(--primary);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .milestone-list {
            list-style-type: none;
            padding-left: 0;
        }

        .milestone-item {
            padding: 12px 0;
            border-bottom: 1px solid var(--light-grey);
            display: flex;
            align-items: center;
            transition: background-color 0.3s ease;
        }

        .milestone-item:hover {
            background-color: #f8f9fa;
            margin: 0 -10px;
            padding-left: 10px;
            padding-right: 10px;
            border-radius: 5px;
        }

        .milestone-item:before {
            content: "•";
            color: var(--primary);
            font-weight: bold;
            display: inline-block;
            width: 1em;
            margin-left: -1em;
            font-size: 1.2rem;
        }

        .status-checkbox {
            margin-left: auto;
            width: 20px;
            height: 20px;
            accent-color: var(--primary);
            cursor: pointer;
        }

        .metrics-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .metric-card {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
            transition: transform 0.3s ease;
        }

        .metric-card:hover {
            transform: translateY(-3px);
        }

        .metric-value {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .metric-card div:last-child {
            font-size: 0.8rem;
            opacity: 0.9;
            font-weight: 500;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .feature-card {
            background-color: white;
            border: 1px solid var(--light-grey);
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .feature-card.completed {
            border-left: 4px solid var(--green);
        }

        .feature-card.in-progress {
            border-left: 4px solid var(--orange);
        }

        .feature-card.planned {
            border-left: 4px solid var(--blue);
        }

        .feature-status {
            font-size: 0.8rem;
            padding: 4px 10px;
            border-radius: 15px;
            color: white;
            display: inline-block;
            margin-bottom: 12px;
            font-weight: 600;
        }

        .status-completed {
            background-color: var(--green);
        }

        .status-in-progress {
            background-color: var(--orange);
        }

        .status-planned {
            background-color: var(--blue);
        }

        .feature-card h3 {
            color: var(--primary);
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .feature-card p {
            color: var(--grey);
            font-size: 0.9rem;
            line-height: 1.5;
        }

        /* Responsive Design */
        @media screen and (max-width: 768px) {
            body {
                padding: 10px;
            }

            .nav-header {
                margin: -10px -10px 20px -10px;
                padding: 1rem;
            }

            .nav-content {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            h1 {
                font-size: 2rem;
            }

            .timeline::after {
                left: 31px;
            }

            .phase {
                width: 100%;
                padding-left: 70px;
                padding-right: 25px;
            }

            .phase::before {
                left: 60px;
                border: medium solid white;
                border-width: 10px 10px 10px 0;
                border-color: transparent white transparent transparent;
            }

            .left::after, .right::after {
                left: 15px;
            }

            .right {
                left: 0%;
            }

            .phase-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .feature-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .metrics-container {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="nav-header">
        <div class="nav-content">
            <a href="solving.html" class="logo">Solving Tomorrow</a>
            <a href="solving.html" class="back-btn">
                <i class="fas fa-arrow-left"></i> Back to Main
            </a>
        </div>
    </div>

    <h1>Lumerous Platform Development Roadmap</h1>

    <div class="timeline">
        <div class="phase left">
            <div class="phase-content">
                <div class="phase-header">
                    <div class="phase-title">Phase 1: Foundation & Launch</div>
                    <div class="phase-date">Jan-Jun 2025</div>
                </div>
                <p>MVP development and initial market entry with R80,000 initial capital</p>
                <ul class="milestone-list">
                    <li class="milestone-item">
                        <span>Complete POPIA compliance and BEE certification</span>
                        <input type="checkbox" class="status-checkbox" checked>
                    </li>
                    <li class="milestone-item">
                        <span>Finalize MVP development with core features</span>
                        <input type="checkbox" class="status-checkbox" checked>
                    </li>
                    <li class="milestone-item">
                        <span>Launch pilot program in first school</span>
                        <input type="checkbox" class="status-checkbox" checked>
                    </li>
                    <li class="milestone-item">
                        <span>Expand to 3 pilot schools, gather feedback</span>
                        <input type="checkbox">
                    </li>
                    <li class="milestone-item">
                        <span>Implement feedback, prepare for public launch</span>
                        <input type="checkbox">
                    </li>
                    <li class="milestone-item">
                        <span>Achieve breakeven: 1,500 users, R300K monthly revenue</span>
                        <input type="checkbox">
                    </li>
                </ul>
                <div class="metrics-container">
                    <div class="metric-card">
                        <div class="metric-value">1,500</div>
                        <div>PAYING USERS</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">3</div>
                        <div>PILOT CLIENTS</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">R300K</div>
                        <div>MONTHLY REVENUE</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">95%</div>
                        <div>USER SATISFACTION</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="phase right">
            <div class="phase-content">
                <div class="phase-header">
                    <div class="phase-title">Phase 2: Market Penetration</div>
                    <div class="phase-date">Jul-Dec 2025</div>
                </div>
                <p>Scale beyond breakeven and implement profit allocation strategy</p>
                <ul class="milestone-list">
                    <li class="milestone-item">
                        <span>Scale to 2,000 users, launch additional services</span>
                        <input type="checkbox">
                    </li>
                    <li class="milestone-item">
                        <span>Implement profit allocation, expand to 10 organizations</span>
                        <input type="checkbox">
                    </li>
                    <li class="milestone-item">
                        <span>Reach 2,500 users, expand service portfolio</span>
                        <input type="checkbox">
                    </li>
                    <li class="milestone-item">
                        <span>Launch referral program</span>
                        <input type="checkbox">
                    </li>
                    <li class="milestone-item">
                        <span>Reach 3,000+ users, expand to 15 client partnerships</span>
                        <input type="checkbox">
                    </li>
                </ul>
                <div class="metrics-container">
                    <div class="metric-card">
                        <div class="metric-value">3,000</div>
                        <div>ACTIVE USERS</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">15</div>
                        <div>CLIENT PARTNERS</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">R600K</div>
                        <div>MONTHLY REVENUE</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">R180K</div>
                        <div>MONTHLY PROFIT</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="phase left">
            <div class="phase-content">
                <div class="phase-header">
                    <div class="phase-title">Phase 3: Rapid Expansion</div>
                    <div class="phase-date">2026</div>
                </div>
                <p>Scale to 5,000+ users across 3 provinces and establish market leadership</p>
                <ul class="milestone-list">
                    <li class="milestone-item">
                        <span>Launch in Western Cape and Eastern Cape</span>
                        <input type="checkbox">
                    </li>
                    <li class="milestone-item">
                        <span>Reach 3,000 users, launch premium tier</span>
                        <input type="checkbox">
                    </li>
                    <li class="milestone-item">
                        <span>50 school partnerships, enterprise features</span>
                        <input type="checkbox">
                    </li>
                    <li class="milestone-item">
                        <span>5,000+ users, market leadership established</span>
                        <input type="checkbox">
                    </li>
                </ul>
                <div class="metrics-container">
                    <div class="metric-card">
                        <div class="metric-value">5,000+</div>
                        <div>ACTIVE USERS</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">50</div>
                        <div>SCHOOL PARTNERS</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">R1M</div>
                        <div>MONTHLY REVENUE</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">30%</div>
                        <div>MARKET SHARE</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="phase right">
            <div class="phase-content">
                <div class="phase-header">
                    <div class="phase-title">Phase 4: Market Leadership & Expansion</div>
                    <div class="phase-date">2027+</div>
                </div>
                <p>Achieve market dominance and prepare for strategic exit opportunities</p>
                <ul class="milestone-list">
                    <li class="milestone-item">
                        <span>Launch in Botswana and Namibia</span>
                        <input type="checkbox">
                    </li>
                    <li class="milestone-item">
                        <span>10,000+ users, advanced AI features</span>
                        <input type="checkbox">
                    </li>
                    <li class="milestone-item">
                        <span>Strategic partnerships with publishers</span>
                        <input type="checkbox">
                    </li>
                    <li class="milestone-item">
                        <span>Evaluate exit opportunities (IPO/Acquisition)</span>
                        <input type="checkbox">
                    </li>
                </ul>
                <div class="metrics-container">
                    <div class="metric-card">
                        <div class="metric-value">10,000+</div>
                        <div>ACTIVE USERS</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">3</div>
                        <div>COUNTRIES</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">R2M+</div>
                        <div>MONTHLY REVENUE</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">50%</div>
                        <div>MARKET SHARE</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <h2>Platform Features Development</h2>
    <div class="feature-grid">
        <div class="feature-card completed">
            <span class="feature-status status-completed">Completed</span>
            <h3>AI-Powered Tutoring</h3>
            <p>Advanced machine learning algorithms provide personalized learning paths</p>
        </div>
        <div class="feature-card completed">
            <span class="feature-status status-completed">Completed</span>
            <h3>Curriculum Alignment</h3>
            <p>100% alignment with CAPS and IEB curricula</p>
        </div>
        <div class="feature-card in-progress">
            <span class="feature-status status-in-progress">In Progress</span>
            <h3>Progress Tracking</h3>
            <p>Detailed analytics and reporting for students and teachers</p>
        </div>
        <div class="feature-card in-progress">
            <span class="feature-status status-in-progress">In Progress</span>
            <h3>Gamified Learning</h3>
            <p>Engagement through badges, leaderboards, and rewards</p>
        </div>
        <div class="feature-card planned">
            <span class="feature-status status-planned">Planned</span>
            <h3>Offline Mode</h3>
            <p>Functionality for areas with limited internet connectivity</p>
        </div>
        <div class="feature-card planned">
            <span class="feature-status status-planned">Planned</span>
            <h3>Teacher Collaboration</h3>
            <p>Tools for resource sharing and professional development</p>
        </div>
        <div class="feature-card planned">
            <span class="feature-status status-planned">Planned</span>
            <h3>Parent Dashboard</h3>
            <p>Monitoring and reporting for parents/guardians</p>
        </div>
        <div class="feature-card planned">
            <span class="feature-status status-planned">Planned</span>
            <h3>Multilingual Support</h3>
            <p>Additional South African language options</p>
        </div>
    </div>

    <script src="js/product-roadmap.js"></script>
</body>
</html>
