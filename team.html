<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Our Team | Solving Tomorrow</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #FF6B35;
            --primary-dark: #E05A2B;
            --secondary: #2D2D2D;
            --light: #F7F7F7;
            --dark: #1A1A1A;
            --grey: #6C757D;
            --light-grey: #E9ECEF;
            --blue: #3498db;
            --green: #2ecc71;
            --orange: #f39c12;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            color: var(--dark);
            background-color: var(--light);
        }

        /* Navigation */
        .nav-header {
            background-color: var(--dark);
            color: var(--light);
            padding: 1rem 2rem;
            margin: -20px -20px 30px -20px;
            border-radius: 0 0 10px 10px;
        }

        .nav-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1160px;
            margin: 0 auto;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary);
            text-decoration: none;
        }

        .back-btn {
            background-color: var(--primary);
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            font-weight: 500;
            transition: background-color 0.3s ease;
        }

        .back-btn:hover {
            background-color: var(--primary-dark);
        }

        h1, h2, h3 {
            color: var(--secondary);
        }

        h1 {
            border-bottom: 3px solid var(--primary);
            padding-bottom: 10px;
            margin-bottom: 30px;
            font-size: 2.5rem;
            font-weight: 700;
            text-align: center;
        }

        .team-intro {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .team-intro p {
            font-size: 1.1rem;
            color: var(--grey);
            max-width: 800px;
            margin: 0 auto;
        }

        .team-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .team-card {
            background-color: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 1px solid var(--light-grey);
            cursor: pointer;
        }

        .team-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .team-header {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .team-header::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            border-top: 10px solid var(--primary-dark);
        }

        .team-name {
            margin: 0;
            font-size: 1.4rem;
            font-weight: 700;
        }

        .team-title {
            margin: 8px 0 0;
            font-size: 1rem;
            opacity: 0.9;
            font-weight: 500;
        }

        .team-photo {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            margin: 20px auto;
            display: block;
            border: 4px solid var(--light-grey);
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .team-card:hover .team-photo {
            transform: scale(1.05);
        }

        .team-content {
            padding: 0 25px 25px;
        }

        .team-bio {
            color: var(--grey);
            font-size: 0.9rem;
            line-height: 1.6;
            margin-bottom: 20px;
            text-align: center;
        }

        .team-expertise {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: center;
            margin-bottom: 20px;
        }

        .expertise-tag {
            background-color: var(--light);
            color: var(--primary);
            padding: 4px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
            border: 1px solid var(--primary);
        }

        .team-contact {
            display: flex;
            justify-content: center;
            gap: 15px;
        }

        .contact-link {
            color: var(--grey);
            font-size: 1.2rem;
            transition: color 0.3s ease, transform 0.3s ease;
        }

        .contact-link:hover {
            color: var(--primary);
            transform: scale(1.2);
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .modal-header {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            padding: 25px;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-close {
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.3s ease;
        }

        .modal-close:hover {
            opacity: 0.7;
        }

        .modal-body {
            padding: 30px;
        }

        .modal-photo {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: block;
            border: 4px solid var(--light-grey);
            object-fit: cover;
        }

        .modal-details h3 {
            color: var(--primary);
            margin-bottom: 15px;
            font-size: 1.2rem;
        }

        .modal-bio {
            color: var(--grey);
            line-height: 1.6;
            margin-bottom: 25px;
        }

        .projects-section {
            margin-top: 25px;
            padding-top: 25px;
            border-top: 1px solid var(--light-grey);
        }

        .projects-section h4 {
            color: var(--secondary);
            margin-bottom: 15px;
            font-size: 1.1rem;
        }

        .project-item {
            background-color: var(--light);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 10px;
            border-left: 4px solid var(--primary);
        }

        .project-title {
            font-weight: 600;
            color: var(--secondary);
            margin-bottom: 5px;
        }

        .project-desc {
            color: var(--grey);
            font-size: 0.9rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .nav-header {
                margin: -10px -10px 20px -10px;
                padding: 1rem;
            }

            .nav-content {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            h1 {
                font-size: 2rem;
            }

            .team-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .modal-content {
                width: 95%;
                margin: 10% auto;
            }

            .modal-body {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="nav-header">
        <div class="nav-content">
            <a href="solving.html" class="logo">Solving Tomorrow</a>
            <a href="solving.html" class="back-btn">
                <i class="fas fa-arrow-left"></i> Back to Main
            </a>
        </div>
    </div>

    <h1>The Solving Tomorrow Dream Team</h1>
    
    <div class="team-intro">
        <p>Meet the passionate innovators behind Solving Tomorrow. Our diverse team combines technical expertise, educational insight, and entrepreneurial spirit to revolutionize learning in South Africa.</p>
    </div>
    
    <div class="team-grid">
        <div class="team-card" data-member="navenshia">
            <div class="team-header">
                <h3 class="team-name">Navenshia</h3>
                <p class="team-title">Junior Full Stack Developer</p>
            </div>
            <img src="https://via.placeholder.com/120/FF6B35/FFFFFF?text=N" alt="Navenshia" class="team-photo">
            <div class="team-content">
                <p class="team-bio">Passionate about creating intuitive user experiences and building scalable web applications.</p>
                <div class="team-expertise">
                    <span class="expertise-tag">React</span>
                    <span class="expertise-tag">Node.js</span>
                    <span class="expertise-tag">UI/UX</span>
                </div>
                <div class="team-contact">
                    <a href="#" class="contact-link"><i class="fab fa-linkedin"></i></a>
                    <a href="#" class="contact-link"><i class="fab fa-github"></i></a>
                    <a href="#" class="contact-link"><i class="fas fa-envelope"></i></a>
                </div>
            </div>
        </div>

        <div class="team-card" data-member="shelden">
            <div class="team-header">
                <h3 class="team-name">Shelden</h3>
                <p class="team-title">CTO & Co-Founder</p>
            </div>
            <img src="https://via.placeholder.com/120/FF6B35/FFFFFF?text=S" alt="Shelden" class="team-photo">
            <div class="team-content">
                <p class="team-bio">Technology visionary with expertise in AI, machine learning, and scalable system architecture.</p>
                <div class="team-expertise">
                    <span class="expertise-tag">AI/ML</span>
                    <span class="expertise-tag">Python</span>
                    <span class="expertise-tag">Cloud Architecture</span>
                </div>
                <div class="team-contact">
                    <a href="#" class="contact-link"><i class="fab fa-linkedin"></i></a>
                    <a href="#" class="contact-link"><i class="fab fa-github"></i></a>
                    <a href="#" class="contact-link"><i class="fas fa-envelope"></i></a>
                </div>
            </div>
        </div>

        <div class="team-card" data-member="magenta">
            <div class="team-header">
                <h3 class="team-name">Magenta</h3>
                <p class="team-title">Full Stack Developer</p>
            </div>
            <img src="https://via.placeholder.com/120/FF6B35/FFFFFF?text=M" alt="Magenta" class="team-photo">
            <div class="team-content">
                <p class="team-bio">Expert in modern web technologies with a focus on performance optimization and clean code.</p>
                <div class="team-expertise">
                    <span class="expertise-tag">JavaScript</span>
                    <span class="expertise-tag">Vue.js</span>
                    <span class="expertise-tag">Database Design</span>
                </div>
                <div class="team-contact">
                    <a href="#" class="contact-link"><i class="fab fa-linkedin"></i></a>
                    <a href="#" class="contact-link"><i class="fab fa-github"></i></a>
                    <a href="#" class="contact-link"><i class="fas fa-envelope"></i></a>
                </div>
            </div>
        </div>

        <div class="team-card" data-member="ashish">
            <div class="team-header">
                <h3 class="team-name">Ashish</h3>
                <p class="team-title">AI Engineer</p>
            </div>
            <img src="https://via.placeholder.com/120/FF6B35/FFFFFF?text=A" alt="Ashish" class="team-photo">
            <div class="team-content">
                <p class="team-bio">Specialized in natural language processing and educational AI systems for personalized learning.</p>
                <div class="team-expertise">
                    <span class="expertise-tag">NLP</span>
                    <span class="expertise-tag">TensorFlow</span>
                    <span class="expertise-tag">Data Science</span>
                </div>
                <div class="team-contact">
                    <a href="#" class="contact-link"><i class="fab fa-linkedin"></i></a>
                    <a href="#" class="contact-link"><i class="fab fa-github"></i></a>
                    <a href="#" class="contact-link"><i class="fas fa-envelope"></i></a>
                </div>
            </div>
        </div>

        <div class="team-card" data-member="rhea">
            <div class="team-header">
                <h3 class="team-name">Rhea</h3>
                <p class="team-title">QA & Testing Lead</p>
            </div>
            <img src="https://via.placeholder.com/120/FF6B35/FFFFFF?text=R" alt="Rhea" class="team-photo">
            <div class="team-content">
                <p class="team-bio">Ensures product quality through comprehensive testing strategies and automated testing frameworks.</p>
                <div class="team-expertise">
                    <span class="expertise-tag">Test Automation</span>
                    <span class="expertise-tag">Quality Assurance</span>
                    <span class="expertise-tag">Selenium</span>
                </div>
                <div class="team-contact">
                    <a href="#" class="contact-link"><i class="fab fa-linkedin"></i></a>
                    <a href="#" class="contact-link"><i class="fab fa-github"></i></a>
                    <a href="#" class="contact-link"><i class="fas fa-envelope"></i></a>
                </div>
            </div>
        </div>

        <div class="team-card" data-member="shivek">
            <div class="team-header">
                <h3 class="team-name">Shivek</h3>
                <p class="team-title">Mobile Developer</p>
            </div>
            <img src="https://via.placeholder.com/120/FF6B35/FFFFFF?text=SH" alt="Shivek" class="team-photo">
            <div class="team-content">
                <p class="team-bio">Creates seamless mobile experiences with expertise in cross-platform development.</p>
                <div class="team-expertise">
                    <span class="expertise-tag">React Native</span>
                    <span class="expertise-tag">Flutter</span>
                    <span class="expertise-tag">Mobile UI</span>
                </div>
                <div class="team-contact">
                    <a href="#" class="contact-link"><i class="fab fa-linkedin"></i></a>
                    <a href="#" class="contact-link"><i class="fab fa-github"></i></a>
                    <a href="#" class="contact-link"><i class="fas fa-envelope"></i></a>
                </div>
            </div>
        </div>

        <div class="team-card" data-member="claude">
            <div class="team-header">
                <h3 class="team-name">Claude</h3>
                <p class="team-title">AI Assistant & Content Specialist</p>
            </div>
            <img src="https://via.placeholder.com/120/FF6B35/FFFFFF?text=C" alt="Claude" class="team-photo">
            <div class="team-content">
                <p class="team-bio">AI-powered content creation and educational material development specialist.</p>
                <div class="team-expertise">
                    <span class="expertise-tag">Content Strategy</span>
                    <span class="expertise-tag">AI Integration</span>
                    <span class="expertise-tag">Educational Design</span>
                </div>
                <div class="team-contact">
                    <a href="#" class="contact-link"><i class="fas fa-robot"></i></a>
                    <a href="#" class="contact-link"><i class="fas fa-brain"></i></a>
                    <a href="#" class="contact-link"><i class="fas fa-envelope"></i></a>
                </div>
            </div>
        </div>

        <div class="team-card" data-member="priyanka">
            <div class="team-header">
                <h3 class="team-name">Priyanka</h3>
                <p class="team-title">UX/UI Designer & Marketing</p>
            </div>
            <img src="https://via.placeholder.com/120/FF6B35/FFFFFF?text=P" alt="Priyanka" class="team-photo">
            <div class="team-content">
                <p class="team-bio">Creates beautiful, user-centered designs and drives marketing strategies for growth.</p>
                <div class="team-expertise">
                    <span class="expertise-tag">UI/UX Design</span>
                    <span class="expertise-tag">Digital Marketing</span>
                    <span class="expertise-tag">Brand Strategy</span>
                </div>
                <div class="team-contact">
                    <a href="#" class="contact-link"><i class="fab fa-linkedin"></i></a>
                    <a href="#" class="contact-link"><i class="fab fa-dribbble"></i></a>
                    <a href="#" class="contact-link"><i class="fas fa-envelope"></i></a>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for detailed team member info -->
    <div id="teamModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalName">Team Member</h2>
                <span class="modal-close">&times;</span>
            </div>
            <div class="modal-body">
                <img src="https://via.placeholder.com/150" alt="Team Member" class="modal-photo" id="modalPhoto">
                <div class="modal-details">
                    <h3 id="modalTitle">Position</h3>
                    <p id="modalBio" class="modal-bio">Detailed bio information will appear here.</p>
                    <div class="team-expertise" id="modalExpertise"></div>
                    <div class="projects-section">
                        <h4>Key Projects</h4>
                        <div class="project-item">
                            <div class="project-title">Lumerous Platform</div>
                            <div class="project-desc">Led development of core AI tutoring features</div>
                        </div>
                        <div class="project-item">
                            <div class="project-title">POPIA Compliance SaaS</div>
                            <div class="project-desc">Designed automated compliance reporting system</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/team.js"></script>
</body>
</html>
