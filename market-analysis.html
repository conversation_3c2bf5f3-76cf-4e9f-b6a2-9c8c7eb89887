<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Market Analysis | Solving Tomorrow</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary: #FF6B35;
            --primary-dark: #E05A2B;
            --secondary: #2D2D2D;
            --light: #F7F7F7;
            --dark: #1A1A1A;
            --grey: #6C757D;
            --light-grey: #E9ECEF;
            --blue: #3498db;
            --green: #2ecc71;
            --orange: #f39c12;
            --red: #e74c3c;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            color: var(--dark);
            background-color: var(--light);
        }

        /* Navigation */
        .nav-header {
            background-color: var(--dark);
            color: var(--light);
            padding: 1rem 2rem;
            margin: -20px -20px 30px -20px;
            border-radius: 0 0 10px 10px;
        }

        .nav-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1160px;
            margin: 0 auto;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary);
            text-decoration: none;
        }

        .back-btn {
            background-color: var(--primary);
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            font-weight: 500;
            transition: background-color 0.3s ease;
        }

        .back-btn:hover {
            background-color: var(--primary-dark);
        }

        h1, h2, h3 {
            color: var(--secondary);
        }

        h1 {
            border-bottom: 3px solid var(--primary);
            padding-bottom: 10px;
            margin-bottom: 30px;
            font-size: 2.5rem;
            font-weight: 700;
        }

        .dashboard-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .metric-card {
            background-color: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
            border: 1px solid var(--light-grey);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .metric-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .metric-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary);
            margin: 10px 0;
        }

        .metric-label {
            font-size: 1rem;
            color: var(--grey);
            font-weight: 500;
        }

        .chart-container {
            background-color: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            height: 400px;
            border: 1px solid var(--light-grey);
        }

        .segment-card {
            background-color: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid var(--primary);
            border: 1px solid var(--light-grey);
        }

        .segment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            flex-wrap: wrap;
            gap: 10px;
        }

        .revenue-tag {
            background-color: var(--primary);
            color: white;
            padding: 8px 15px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .driver-card {
            background-color: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 15px;
            border: 1px solid var(--light-grey);
            transition: transform 0.3s ease;
        }

        .driver-card:hover {
            transform: translateY(-3px);
        }

        .driver-icon {
            font-size: 2rem;
            margin-right: 15px;
        }

        .driver-content {
            display: flex;
            align-items: flex-start;
        }

        .driver-content h3 {
            color: var(--primary);
            margin-bottom: 8px;
        }

        .competitor-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background-color: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .competitor-table th, .competitor-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid var(--light-grey);
        }

        .competitor-table th {
            background-color: var(--primary);
            color: white;
            font-weight: 600;
        }

        .competitor-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .competitive-matrix {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background-color: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .competitive-matrix th, .competitive-matrix td {
            padding: 15px;
            text-align: center;
            border: 1px solid var(--light-grey);
        }

        .competitive-matrix th {
            background-color: var(--primary);
            color: white;
            font-weight: 600;
        }

        .strength {
            background-color: var(--green);
            color: white;
            font-weight: 600;
            border-radius: 5px;
        }

        .weakness {
            background-color: var(--red);
            color: white;
            font-weight: 600;
            border-radius: 5px;
        }

        .neutral {
            background-color: var(--orange);
            color: white;
            font-weight: 600;
            border-radius: 5px;
        }

        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 2px solid var(--light-grey);
            flex-wrap: wrap;
        }

        .tab {
            padding: 12px 24px;
            cursor: pointer;
            background-color: var(--light-grey);
            margin-right: 5px;
            border-radius: 8px 8px 0 0;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .tab.active {
            background-color: var(--primary);
            color: white;
        }

        .tab:hover:not(.active) {
            background-color: #ddd;
        }

        .tab-content {
            display: none;
            background-color: white;
            border-radius: 0 12px 12px 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .tab-content.active {
            display: block;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .nav-header {
                margin: -10px -10px 20px -10px;
                padding: 1rem;
            }

            .nav-content {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            h1 {
                font-size: 2rem;
            }

            .dashboard-container {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .segment-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .tabs {
                flex-direction: column;
            }

            .tab {
                margin-right: 0;
                margin-bottom: 5px;
                border-radius: 8px;
            }

            .tab-content {
                border-radius: 12px;
            }

            .competitor-table, .competitive-matrix {
                font-size: 0.9rem;
            }

            .competitor-table th, .competitor-table td,
            .competitive-matrix th, .competitive-matrix td {
                padding: 10px 8px;
            }
        }
    </style>
</head>
<body>
    <div class="nav-header">
        <div class="nav-content">
            <a href="solving.html" class="logo">Solving Tomorrow</a>
            <a href="solving.html" class="back-btn">
                <i class="fas fa-arrow-left"></i> Back to Main
            </a>
        </div>
    </div>

    <h1>South African EdTech Market Analysis</h1>
    
    <div class="dashboard-container">
        <div class="metric-card">
            <div class="metric-value">R2.8B</div>
            <div class="metric-label">Market Size (2025)</div>
        </div>
        <div class="metric-card">
            <div class="metric-value">15%</div>
            <div class="metric-label">Annual Growth Rate</div>
        </div>
        <div class="metric-card">
            <div class="metric-value">12.3M</div>
            <div class="metric-label">Target Students</div>
        </div>
        <div class="metric-card">
            <div class="metric-value">78%</div>
            <div class="metric-label">Digital Adoption</div>
        </div>
    </div>
    
    <div class="chart-container">
        <canvas id="marketGrowthChart"></canvas>
    </div>

    <h2>Key Market Drivers</h2>
    <div class="dashboard-container">
        <div class="driver-card">
            <div class="driver-content">
                <div class="driver-icon">📈</div>
                <div>
                    <h3>Post-COVID Digital Transformation</h3>
                    <p>Accelerated adoption of digital learning tools and remote education capabilities</p>
                </div>
            </div>
        </div>
        <div class="driver-card">
            <div class="driver-content">
                <div class="driver-icon">🏛️</div>
                <div>
                    <h3>Government Investment</h3>
                    <p>R2.5 billion allocated for education technology initiatives (2025-2027)</p>
                </div>
            </div>
        </div>
        <div class="driver-card">
            <div class="driver-content">
                <div class="driver-icon">👩‍🏫</div>
                <div>
                    <h3>Teacher Shortage Crisis</h3>
                    <p>30,000 teacher shortage creates demand for AI-assisted learning solutions</p>
                </div>
            </div>
        </div>
    </div>

    <h2>Target Market Segmentation</h2>
    <div class="segment-card">
        <div class="segment-header">
            <h3>Primary Segment: Individual Students & Tutors</h3>
            <span class="revenue-tag">R10M/month potential</span>
        </div>
        <ul>
            <li><strong>Size:</strong> 2M students (Grades 8-12)</li>
            <li><strong>Characteristics:</strong> Ages 13-18, smartphone access, academic pressure</li>
            <li><strong>Revenue Model:</strong> R200/month × 50,000 users = R10M/month</li>
            <li><strong>Focus:</strong> Urban/semi-urban areas with reliable internet</li>
        </ul>
    </div>

    <div class="segment-card">
        <div class="segment-header">
            <h3>Secondary Segment: Schools & Institutions</h3>
            <span class="revenue-tag">R2.5M/month potential</span>
        </div>
        <ul>
            <li><strong>Size:</strong> 1,200 high schools (Grades 8-12)</li>
            <li><strong>Characteristics:</strong> Private/Model C schools with technology budgets</li>
            <li><strong>Revenue Model:</strong> R5,000/month × 500 schools = R2.5M/month</li>
            <li><strong>Focus:</strong> Progressive principals seeking competitive advantages</li>
        </ul>
    </div>

    <div class="segment-card">
        <div class="segment-header">
            <h3>Tertiary Segment: Tutoring Centers & NGOs</h3>
            <span class="revenue-tag">R600K/month potential</span>
        </div>
        <ul>
            <li><strong>Size:</strong> 800 tutoring centers nationwide</li>
            <li><strong>Characteristics:</strong> Education-focused NGOs and after-school programs</li>
            <li><strong>Revenue Model:</strong> R3,000/month × 200 centers = R600K/month</li>
            <li><strong>Focus:</strong> Organizations with education-focused mandates</li>
        </ul>
    </div>

    <h2>Competitive Landscape</h2>
    <div class="tabs">
        <div class="tab active" data-tab="competitors">Key Competitors</div>
        <div class="tab" data-tab="matrix">Positioning Matrix</div>
        <div class="tab" data-tab="advantages">Our Advantages</div>
    </div>

    <div class="tab-content active" id="competitors">
        <table class="competitor-table">
            <thead>
                <tr>
                    <th>Competitor</th>
                    <th>Location</th>
                    <th>Market Share</th>
                    <th>Strengths</th>
                    <th>Weaknesses</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Mindset Learn</td>
                    <td>Cape Town</td>
                    <td>25%</td>
                    <td>CAPS-aligned, school distribution network</td>
                    <td>Limited personalization, outdated UI</td>
                </tr>
                <tr>
                    <td>Siyavula</td>
                    <td>Cape Town</td>
                    <td>20%</td>
                    <td>Strong adaptive learning for math/science</td>
                    <td>Limited to STEM subjects</td>
                </tr>
                <tr>
                    <td>Global Players</td>
                    <td>International</td>
                    <td>30%</td>
                    <td>Advanced AI, global brand recognition</td>
                    <td>No CAPS alignment, generic content</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="tab-content" id="matrix">
        <table class="competitive-matrix">
            <thead>
                <tr>
                    <th>Factor</th>
                    <th>Solving Tomorrow</th>
                    <th>Mindset Learn</th>
                    <th>Siyavula</th>
                    <th>Global Players</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>CAPS/IEB Alignment</td>
                    <td class="strength">Strong</td>
                    <td class="strength">Strong</td>
                    <td class="strength">Strong</td>
                    <td class="weakness">Poor</td>
                </tr>
                <tr>
                    <td>AI Integration</td>
                    <td class="strength">Strong</td>
                    <td class="weakness">Poor</td>
                    <td class="neutral">Fair</td>
                    <td class="strength">Strong</td>
                </tr>
                <tr>
                    <td>Local Understanding</td>
                    <td class="strength">Strong</td>
                    <td class="strength">Strong</td>
                    <td class="strength">Strong</td>
                    <td class="weakness">Poor</td>
                </tr>
                <tr>
                    <td>Technology Innovation</td>
                    <td class="strength">Strong</td>
                    <td class="neutral">Fair</td>
                    <td class="strength">Strong</td>
                    <td class="strength">Strong</td>
                </tr>
                <tr>
                    <td>Pricing</td>
                    <td class="strength">Strong</td>
                    <td class="neutral">Fair</td>
                    <td class="weakness">Poor</td>
                    <td class="weakness">Poor</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="tab-content" id="advantages">
        <div class="segment-card">
            <h3>Our Sustainable Competitive Advantages</h3>
            <ul>
                <li><strong>Deep Local Expertise:</strong> CAPS/IEB alignment with native language support</li>
                <li><strong>AI-Powered Personalization:</strong> Advanced chatbot tutoring in local context</li>
                <li><strong>Cost Leadership:</strong> R200/month vs competitors' R300–500/month</li>
                <li><strong>Mobile-First Design:</strong> Optimized for South African smartphone usage</li>
                <li><strong>Community Integration:</strong> Built-in peer learning and teacher collaboration</li>
            </ul>
        </div>

        <div class="segment-card">
            <h3>How We Will Beat Competitors</h3>
            <ul>
                <li><strong>Speed to Market:</strong> Faster implementation (2 weeks vs 3 months)</li>
                <li><strong>Local Support:</strong> Durban-based team with same-day support</li>
                <li><strong>Flexible Pricing:</strong> Multiple payment options including mobile money</li>
                <li><strong>Continuous Innovation:</strong> Monthly feature updates based on feedback</li>
                <li><strong>Partnership Strategy:</strong> Direct relationships with teacher unions</li>
            </ul>
        </div>
    </div>

    <script src="js/market-analysis.js"></script>
</body>
</html>
