document.addEventListener('DOMContentLoaded', function() {
    // Team member data
    const teamData = {
        navenshia: {
            name: "<PERSON><PERSON><PERSON><PERSON>",
            title: "Junior Full Stack Developer",
            bio: "<PERSON><PERSON><PERSON><PERSON> is a passionate junior developer with a keen eye for detail and a love for creating intuitive user experiences. She specializes in modern web technologies and is constantly learning new frameworks to stay at the forefront of web development. Her dedication to clean code and user-centered design makes her an invaluable member of our development team.",
            expertise: ["React", "Node.js", "UI/UX", "JavaScript", "CSS3", "MongoDB"],
            photo: "https://via.placeholder.com/150/FF6B35/FFFFFF?text=N"
        },
        shelden: {
            name: "<PERSON><PERSON><PERSON>",
            title: "<PERSON><PERSON> & Co-Founder",
            bio: "<PERSON><PERSON><PERSON> is our technology visionary and co-founder, bringing years of experience in AI, machine learning, and scalable system architecture. He leads our technical strategy and ensures our platform leverages cutting-edge technologies to deliver exceptional educational experiences. His expertise in cloud computing and AI has been instrumental in building our robust platform.",
            expertise: ["AI/ML", "Python", "Cloud Architecture", "TensorFlow", "AWS", "System Design"],
            photo: "https://via.placeholder.com/150/FF6B35/FFFFFF?text=S"
        },
        magenta: {
            name: "Magenta",
            title: "Full Stack Developer",
            bio: "Magenta is an expert full-stack developer with a passion for performance optimization and clean, maintainable code. She has extensive experience in modern web technologies and database design, ensuring our applications are both fast and reliable. Her attention to detail and problem-solving skills make her an essential part of our development team.",
            expertise: ["JavaScript", "Vue.js", "Database Design", "PostgreSQL", "Redis", "Docker"],
            photo: "https://via.placeholder.com/150/FF6B35/FFFFFF?text=M"
        },
        ashish: {
            name: "Ashish",
            title: "AI Engineer",
            bio: "Ashish specializes in natural language processing and educational AI systems, focusing on creating personalized learning experiences. His deep understanding of machine learning algorithms and data science enables him to build intelligent systems that adapt to each student's learning style. He's passionate about using AI to democratize education.",
            expertise: ["NLP", "TensorFlow", "Data Science", "PyTorch", "Scikit-learn", "Deep Learning"],
            photo: "https://via.placeholder.com/150/FF6B35/FFFFFF?text=A"
        },
        rhea: {
            name: "Rhea",
            title: "QA & Testing Lead",
            bio: "Rhea ensures our product quality through comprehensive testing strategies and automated testing frameworks. Her meticulous approach to quality assurance and her expertise in test automation help us deliver bug-free, reliable software. She's passionate about creating robust testing processes that scale with our growing platform.",
            expertise: ["Test Automation", "Quality Assurance", "Selenium", "Jest", "Cypress", "Performance Testing"],
            photo: "https://via.placeholder.com/150/FF6B35/FFFFFF?text=R"
        },
        shivek: {
            name: "Shivek",
            title: "Mobile Developer",
            bio: "Shivek creates seamless mobile experiences with expertise in cross-platform development. His focus on mobile UI/UX and performance optimization ensures our mobile applications provide excellent user experiences across all devices. He's passionate about mobile-first design and accessibility.",
            expertise: ["React Native", "Flutter", "Mobile UI", "iOS", "Android", "Mobile Performance"],
            photo: "https://via.placeholder.com/150/FF6B35/FFFFFF?text=SH"
        },
        claude: {
            name: "Claude",
            title: "AI Assistant & Content Specialist",
            bio: "Claude is our AI-powered content creation and educational material development specialist. With advanced natural language capabilities, Claude helps create engaging educational content, assists in curriculum development, and provides intelligent tutoring support. Claude represents the cutting-edge of AI integration in education.",
            expertise: ["Content Strategy", "AI Integration", "Educational Design", "Natural Language", "Curriculum Development", "AI Tutoring"],
            photo: "https://via.placeholder.com/150/FF6B35/FFFFFF?text=C"
        },
        priyanka: {
            name: "Priyanka",
            title: "UX/UI Designer & Marketing",
            bio: "Priyanka creates beautiful, user-centered designs and drives our marketing strategies for growth. Her expertise in design thinking and user research ensures our products are not only functional but also delightful to use. She combines creative design with data-driven marketing to help us reach and engage our target audience effectively.",
            expertise: ["UI/UX Design", "Digital Marketing", "Brand Strategy", "Figma", "Adobe Creative Suite", "User Research"],
            photo: "https://via.placeholder.com/150/FF6B35/FFFFFF?text=P"
        }
    };

    // Modal elements
    const modal = document.getElementById('teamModal');
    const modalName = document.getElementById('modalName');
    const modalTitle = document.getElementById('modalTitle');
    const modalBio = document.getElementById('modalBio');
    const modalPhoto = document.getElementById('modalPhoto');
    const modalExpertise = document.getElementById('modalExpertise');
    const modalClose = document.querySelector('.modal-close');

    // Team card click handlers
    const teamCards = document.querySelectorAll('.team-card');
    teamCards.forEach(card => {
        card.addEventListener('click', function() {
            const memberId = this.getAttribute('data-member');
            const member = teamData[memberId];
            
            if (member) {
                // Populate modal with member data
                modalName.textContent = member.name;
                modalTitle.textContent = member.title;
                modalBio.textContent = member.bio;
                modalPhoto.src = member.photo;
                modalPhoto.alt = member.name;
                
                // Clear and populate expertise tags
                modalExpertise.innerHTML = '';
                member.expertise.forEach(skill => {
                    const tag = document.createElement('span');
                    tag.className = 'expertise-tag';
                    tag.textContent = skill;
                    modalExpertise.appendChild(tag);
                });
                
                // Show modal
                modal.style.display = 'block';
                document.body.style.overflow = 'hidden';
                
                // Add animation
                setTimeout(() => {
                    modal.querySelector('.modal-content').style.transform = 'scale(1)';
                    modal.querySelector('.modal-content').style.opacity = '1';
                }, 10);
            }
        });
    });

    // Close modal handlers
    modalClose.addEventListener('click', closeModal);
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal();
        }
    });

    // Close modal with escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && modal.style.display === 'block') {
            closeModal();
        }
    });

    function closeModal() {
        modal.querySelector('.modal-content').style.transform = 'scale(0.8)';
        modal.querySelector('.modal-content').style.opacity = '0';
        
        setTimeout(() => {
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }, 300);
    }

    // Initialize modal styles
    modal.querySelector('.modal-content').style.transform = 'scale(0.8)';
    modal.querySelector('.modal-content').style.opacity = '0';
    modal.querySelector('.modal-content').style.transition = 'all 0.3s ease';

    // Animate team cards on scroll
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, { 
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });

    teamCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = `all 0.6s ease-out ${index * 0.1}s`;
        observer.observe(card);
    });

    // Add hover sound effect (optional)
    teamCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            // Add subtle hover effect
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // Animate team intro
    const teamIntro = document.querySelector('.team-intro');
    if (teamIntro) {
        teamIntro.style.opacity = '0';
        teamIntro.style.transform = 'translateY(20px)';
        teamIntro.style.transition = 'all 0.8s ease-out';
        
        setTimeout(() => {
            teamIntro.style.opacity = '1';
            teamIntro.style.transform = 'translateY(0)';
        }, 300);
    }

    // Add click animation to contact links
    const contactLinks = document.querySelectorAll('.contact-link');
    contactLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            // Add click animation
            this.style.transform = 'scale(1.3)';
            setTimeout(() => {
                this.style.transform = 'scale(1.2)';
            }, 150);
            
            // Here you could add actual contact functionality
            console.log('Contact link clicked:', this.innerHTML);
        });
    });

    // Add typing effect to team intro text
    function typeWriter(element, text, speed = 50) {
        let i = 0;
        element.innerHTML = '';
        
        function type() {
            if (i < text.length) {
                element.innerHTML += text.charAt(i);
                i++;
                setTimeout(type, speed);
            }
        }
        type();
    }

    // Initialize typing effect for intro (optional)
    const introText = teamIntro?.querySelector('p');
    if (introText) {
        const originalText = introText.textContent;
        setTimeout(() => {
            typeWriter(introText, originalText, 30);
        }, 800);
    }
});
