<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Client Lifecycle Management | Solving Tomorrow</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #FF6B35;
            --primary-dark: #E05A2B;
            --secondary: #2D2D2D;
            --light: #F7F7F7;
            --dark: #1A1A1A;
            --grey: #6C757D;
            --light-grey: #E9ECEF;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background-color: var(--light);
            color: var(--dark);
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
            margin-bottom: 2rem;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--secondary);
            text-decoration: none;
        }

        .logo span {
            color: var(--primary);
        }

        .back-link {
            display: flex;
            align-items: center;
            color: var(--primary);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s;
        }

        .back-link:hover {
            color: var(--primary-dark);
        }

        .back-link i {
            margin-right: 0.5rem;
        }

        h1 {
            color: var(--secondary);
            border-bottom: 2px solid var(--primary);
            padding-bottom: 10px;
            margin-bottom: 2rem;
            font-size: 2.5rem;
        }

        .phase {
            background-color: white;
            border-left: 4px solid var(--primary);
            padding: 2rem;
            margin: 2rem 0;
            border-radius: 0 15px 15px 0;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .phase h2 {
            margin-top: 0;
            color: var(--primary);
            font-size: 1.8rem;
            margin-bottom: 1.5rem;
        }

        .task-list {
            list-style-type: none;
        }

        .task-list li {
            padding: 1rem 0;
            border-bottom: 1px solid var(--light-grey);
            display: flex;
            align-items: center;
            font-size: 1.1rem;
        }

        .task-list li:before {
            content: "\f00c";
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            color: var(--primary);
            margin-right: 1rem;
        }

        .status-btn {
            margin-left: auto;
            padding: 0.5rem 1rem;
            border-radius: 30px;
            border: none;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .pending {
            background-color: var(--light-grey);
            color: var(--grey);
        }

        .completed {
            background-color: var(--primary);
            color: white;
        }

        #progress-container {
            margin: 3rem 0 1rem;
            background-color: var(--light-grey);
            border-radius: 30px;
            height: 25px;
            overflow: hidden;
        }

        #progress-bar {
            background-color: var(--primary);
            height: 100%;
            width: 0%;
            border-radius: 30px;
            transition: width 0.5s;
        }

        #progress-text {
            text-align: center;
            margin-top: 1rem;
            font-weight: 600;
            color: var(--secondary);
            font-size: 1.1rem;
        }
    </style>
</head>
<body>
    <header>
        <a href="solving.html" class="logo">Solving<span>Tomorrow</span></a>
        <a href="solving.html" class="back-link">
            <i class="fas fa-arrow-left"></i> Back to Main Site
        </a>
    </header>

    <h1>Client Lifecycle Management</h1>
    
    <div class="phase">
        <h2>Before Client Engagement</h2>
        <ul class="task-list" id="before-tasks">
            <li>Initial contact and needs assessment <button class="status-btn pending">Pending</button></li>
            <li>Proposal development and presentation <button class="status-btn pending">Pending</button></li>
            <li>Contract negotiation and signing <button class="status-btn pending">Pending</button></li>
            <li>Onboarding process initiation <button class="status-btn pending">Pending</button></li>
        </ul>
    </div>
    
    <div class="phase">
        <h2>During Client Engagement</h2>
        <ul class="task-list" id="during-tasks">
            <li>Regular progress meetings <button class="status-btn pending">Pending</button></li>
            <li>Quality assurance checks <button class="status-btn pending">Pending</button></li>
            <li>Milestone delivery and review <button class="status-btn pending">Pending</button></li>
            <li>Ongoing communication and updates <button class="status-btn pending">Pending</button></li>
        </ul>
    </div>
    
    <div class="phase">
        <h2>Client Aftercare</h2>
        <ul class="task-list" id="after-tasks">
            <li>Final deliverables handover <button class="status-btn pending">Pending</button></li>
            <li>Post-project evaluation <button class="status-btn pending">Pending</button></li>
            <li>Satisfaction survey <button class="status-btn pending">Pending</button></li>
            <li>Follow-up for future opportunities <button class="status-btn pending">Pending</button></li>
            <li>Loyalty program introduction <button class="status-btn pending">Pending</button></li>
        </ul>
    </div>
    
    <div id="progress-container">
        <div id="progress-bar"></div>
    </div>
    <div id="progress-text">0% Complete</div>
    
    <script src="client_lifecycle.js"></script>
</body>
</html>