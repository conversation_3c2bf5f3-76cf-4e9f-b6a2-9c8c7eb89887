<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Revenue Model | Solving Tomorrow</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary: #FF6B35;
            --primary-dark: #E05A2B;
            --secondary: #2D2D2D;
            --light: #F7F7F7;
            --dark: #1A1A1A;
            --grey: #6C757D;
            --light-grey: #E9ECEF;
            --blue: #3498db;
            --green: #2ecc71;
            --orange: #f39c12;
            --red: #e74c3c;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            line-height: 1.6;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            color: var(--dark);
            background-color: var(--light);
        }

        /* Navigation */
        .nav-header {
            background-color: var(--dark);
            color: var(--light);
            padding: 1rem 2rem;
            margin: -20px -20px 30px -20px;
            border-radius: 0 0 10px 10px;
        }

        .nav-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 960px;
            margin: 0 auto;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary);
            text-decoration: none;
        }

        .back-btn {
            background-color: var(--primary);
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            font-weight: 500;
            transition: background-color 0.3s ease;
        }

        .back-btn:hover {
            background-color: var(--primary-dark);
        }

        h1, h2, h3 {
            color: var(--secondary);
        }

        h1 {
            border-bottom: 3px solid var(--primary);
            padding-bottom: 10px;
            margin-bottom: 30px;
            font-size: 2.5rem;
            font-weight: 700;
        }

        .revenue-stream {
            background-color: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid var(--primary);
            border: 1px solid var(--light-grey);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .revenue-stream:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .revenue-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            flex-wrap: wrap;
            gap: 10px;
        }

        .revenue-header h2 {
            color: var(--primary);
            font-size: 1.4rem;
            margin: 0;
        }

        .price-tag {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            padding: 8px 15px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 0.9rem;
            box-shadow: 0 2px 10px rgba(255, 107, 53, 0.3);
        }

        .revenue-stream p {
            color: var(--grey);
            margin-bottom: 15px;
            line-height: 1.6;
        }

        .revenue-stream ul {
            color: var(--grey);
            padding-left: 20px;
        }

        .revenue-stream li {
            margin-bottom: 8px;
        }

        .projection-chart {
            height: 400px;
            background-color: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: 1px solid var(--light-grey);
        }

        .projection-chart h2 {
            color: var(--primary);
            margin-bottom: 20px;
            text-align: center;
            font-size: 1.5rem;
        }

        .calculator {
            background-color: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: 1px solid var(--light-grey);
        }

        .calculator h2 {
            color: var(--primary);
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--secondary);
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid var(--light-grey);
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: var(--primary);
        }

        .range-group {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 15px;
            align-items: center;
        }

        .range-input {
            width: 100%;
        }

        .range-value {
            background-color: var(--primary);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-weight: 600;
            min-width: 60px;
            text-align: center;
        }

        .results {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }

        .results h3 {
            color: white;
            margin-bottom: 15px;
        }

        .result-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }

        .result-item:last-child {
            border-bottom: none;
        }

        .financial-table {
            background-color: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: 1px solid var(--light-grey);
            overflow-x: auto;
        }

        .financial-table h2 {
            color: var(--primary);
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        table th, table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid var(--light-grey);
        }

        table th {
            background-color: var(--primary);
            color: white;
            font-weight: 600;
        }

        table tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        table tr:hover {
            background-color: #f1f3f4;
        }

        .profit-allocation {
            background-color: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: 1px solid var(--light-grey);
        }

        .profit-allocation h2 {
            color: var(--primary);
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        .allocation-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .allocation-item {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid var(--primary);
            transition: transform 0.3s ease;
        }

        .allocation-item:hover {
            transform: translateY(-3px);
        }

        .allocation-item h4 {
            color: var(--primary);
            margin-bottom: 10px;
        }

        .allocation-item .percentage {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--secondary);
            margin-bottom: 8px;
        }

        .milestone-timeline {
            background-color: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: 1px solid var(--light-grey);
        }

        .milestone-timeline h2 {
            color: var(--primary);
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        .milestone {
            display: flex;
            align-items: center;
            padding: 15px;
            margin-bottom: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid var(--green);
            transition: transform 0.3s ease;
        }

        .milestone:hover {
            transform: translateX(5px);
        }

        .milestone-icon {
            background-color: var(--green);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: bold;
        }

        .milestone-content h4 {
            color: var(--secondary);
            margin-bottom: 5px;
        }

        .milestone-content p {
            color: var(--grey);
            font-size: 0.9rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .nav-header {
                margin: -10px -10px 20px -10px;
                padding: 1rem;
            }

            .nav-content {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            h1 {
                font-size: 2rem;
            }

            .revenue-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .range-group {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .allocation-grid {
                grid-template-columns: 1fr;
            }

            .milestone {
                flex-direction: column;
                text-align: center;
            }

            .milestone-icon {
                margin-right: 0;
                margin-bottom: 10px;
            }

            table {
                font-size: 0.9rem;
            }

            table th, table td {
                padding: 8px 10px;
            }
        }
    </style>
</head>
<body>
    <div class="nav-header">
        <div class="nav-content">
            <a href="solving.html" class="logo">Solving Tomorrow</a>
            <a href="solving.html" class="back-btn">
                <i class="fas fa-arrow-left"></i> Back to Main
            </a>
        </div>
    </div>

    <h1>Solving Tomorrow Revenue Model</h1>

    <div class="revenue-stream">
        <div class="revenue-header">
            <h2>Lumerous Education Platform</h2>
            <span class="price-tag">R200/month per user</span>
        </div>
        <p>Our flagship AI-powered tutoring platform targeting individual students and tutors in South Africa.</p>
        <ul>
            <li>Personalized AI tutoring for CAPS and IEB curricula</li>
            <li>Interactive learning modules and assessments</li>
            <li>Progress tracking and analytics</li>
            <li>Mobile-first design for accessibility</li>
        </ul>
    </div>

    <div class="revenue-stream">
        <div class="revenue-header">
            <h2>Enterprise Licenses</h2>
            <span class="price-tag">R5,000/month per organization</span>
        </div>
        <p>Comprehensive educational solutions for schools, tutoring centers, and educational institutions.</p>
        <ul>
            <li>Multi-user management and administration</li>
            <li>Custom branding and white-label options</li>
            <li>Advanced analytics and reporting</li>
            <li>Priority support and training</li>
        </ul>
    </div>

    <div class="revenue-stream">
        <div class="revenue-header">
            <h2>Premium Features</h2>
            <span class="price-tag">R50-100/month add-ons</span>
        </div>
        <p>Enhanced features and services for users seeking advanced functionality.</p>
        <ul>
            <li>Advanced AI tutoring with voice interaction</li>
            <li>Offline mode for limited connectivity areas</li>
            <li>Priority customer support</li>
            <li>Extended content library and resources</li>
        </ul>
    </div>

    <div class="revenue-stream">
        <div class="revenue-header">
            <h2>POPIA Compliance SaaS</h2>
            <span class="price-tag">R2,000–5,000/month</span>
        </div>
        <p>Automated compliance management system for South African businesses.</p>
        <ul>
            <li>Automated POPIA compliance monitoring</li>
            <li>Data protection impact assessments</li>
            <li>Compliance reporting and documentation</li>
            <li>Regular updates for regulatory changes</li>
        </ul>
    </div>

    <div class="revenue-stream">
        <div class="revenue-header">
            <h2>E-commerce Solutions</h2>
            <span class="price-tag">R50,000 one-time + R2,000/month</span>
        </div>
        <p>Custom e-commerce platforms with integrated AI features for South African businesses.</p>
        <ul>
            <li>AI-powered product recommendations</li>
            <li>Inventory management and analytics</li>
            <li>Payment gateway integration</li>
            <li>Mobile-responsive design</li>
        </ul>
    </div>

    <div class="projection-chart">
        <h2>3-Year Revenue Projections</h2>
        <canvas id="revenueChart"></canvas>
    </div>

    <div class="calculator">
        <h2>Revenue Projection Calculator</h2>
        <div class="form-group">
            <label for="newUsers">New Users Per Month:</label>
            <input type="number" id="newUsers" value="500" min="0" max="10000">
        </div>
        <div class="form-group">
            <label for="premiumRate">Premium Adoption Rate:</label>
            <div class="range-group">
                <input type="range" id="premiumRate" class="range-input" min="0" max="50" value="20">
                <span id="premiumRateValue" class="range-value">20%</span>
            </div>
        </div>
        <div class="form-group">
            <label for="newSchools">New Schools Per Month:</label>
            <input type="number" id="newSchools" value="5" min="0" max="100">
        </div>
        <div class="form-group">
            <label for="churnRate">Monthly Churn Rate:</label>
            <div class="range-group">
                <input type="range" id="churnRate" class="range-input" min="0" max="20" value="5">
                <span id="churnRateValue" class="range-value">5%</span>
            </div>
        </div>
        <div class="results">
            <h3>Projected Monthly Results</h3>
            <div class="result-item">
                <span>Monthly Revenue:</span>
                <span id="projectedRevenue">R0</span>
            </div>
            <div class="result-item">
                <span>Breakeven Point:</span>
                <span id="breakevenPoint">Calculating...</span>
            </div>
        </div>
    </div>

    <div class="financial-table">
        <h2>Financial Projections</h2>
        <table>
            <thead>
                <tr>
                    <th>Metric</th>
                    <th>Year 1</th>
                    <th>Year 2</th>
                    <th>Year 3</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Total Users</td>
                    <td>1,500</td>
                    <td>3,000</td>
                    <td>6,000</td>
                </tr>
                <tr>
                    <td>Enterprise Clients</td>
                    <td>10</td>
                    <td>25</td>
                    <td>50</td>
                </tr>
                <tr>
                    <td>Monthly Revenue</td>
                    <td>R300,000</td>
                    <td>R600,000</td>
                    <td>R1,200,000</td>
                </tr>
                <tr>
                    <td>Annual Revenue</td>
                    <td>R3,600,000</td>
                    <td>R7,200,000</td>
                    <td>R14,400,000</td>
                </tr>
                <tr>
                    <td>Operating Costs</td>
                    <td>R2,400,000</td>
                    <td>R4,320,000</td>
                    <td>R7,200,000</td>
                </tr>
                <tr>
                    <td>Net Profit</td>
                    <td>R1,200,000</td>
                    <td>R2,880,000</td>
                    <td>R7,200,000</td>
                </tr>
                <tr>
                    <td>Profit Margin</td>
                    <td>33%</td>
                    <td>40%</td>
                    <td>50%</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="profit-allocation">
        <h2>Profit Allocation Framework</h2>
        <p>Once Lumerous achieves R300,000/month (Month 5), profits will be strategically allocated:</p>
        <div class="allocation-grid">
            <div class="allocation-item">
                <h4>Reinvestment & Growth</h4>
                <div class="percentage">40%</div>
                <p>Platform development, marketing, and expansion into new markets</p>
            </div>
            <div class="allocation-item">
                <h4>Team & Operations</h4>
                <div class="percentage">35%</div>
                <p>Salaries, benefits, and operational expenses for sustainable growth</p>
            </div>
            <div class="allocation-item">
                <h4>Emergency Fund</h4>
                <div class="percentage">15%</div>
                <p>Financial buffer for unexpected challenges and opportunities</p>
            </div>
            <div class="allocation-item">
                <h4>Founder Distribution</h4>
                <div class="percentage">10%</div>
                <p>Rewards for founders based on performance and milestones</p>
            </div>
        </div>
    </div>

    <div class="milestone-timeline">
        <h2>Key Financial Milestones</h2>
        <div class="milestone">
            <div class="milestone-icon">1</div>
            <div class="milestone-content">
                <h4>Month 5: Breakeven Achievement</h4>
                <p>Reach 1,500 paying users and R300,000 monthly revenue</p>
            </div>
        </div>
        <div class="milestone">
            <div class="milestone-icon">2</div>
            <div class="milestone-content">
                <h4>Month 12: Profit Optimization</h4>
                <p>Scale to 2,000+ users with 30% profit margin</p>
            </div>
        </div>
        <div class="milestone">
            <div class="milestone-icon">3</div>
            <div class="milestone-content">
                <h4>Year 2: Market Leadership</h4>
                <p>Achieve R600,000 monthly revenue with 3,000+ users</p>
            </div>
        </div>
        <div class="milestone">
            <div class="milestone-icon">4</div>
            <div class="milestone-content">
                <h4>Year 3: Regional Expansion</h4>
                <p>Launch in neighboring countries with R1.2M monthly revenue</p>
            </div>
        </div>
    </div>

    <script src="js/revenue-model.js"></script>
</body>
</html>
