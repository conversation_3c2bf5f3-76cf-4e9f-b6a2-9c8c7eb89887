document.addEventListener('DOMContentLoaded', function() {
    // Initialize market growth chart
    const ctx = document.getElementById('marketGrowthChart').getContext('2d');
    const marketChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['2023', '2024', '2025', '2026', '2027', '2028'],
            datasets: [{
                label: 'South African EdTech Market Size (R billions)',
                data: [1.8, 2.1, 2.8, 3.2, 3.7, 4.3],
                borderColor: '#FF6B35',
                backgroundColor: 'rgba(255, 107, 53, 0.1)',
                fill: true,
                tension: 0.3,
                borderWidth: 3,
                pointBackgroundColor: '#FF6B35',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'South African EdTech Market Growth Projection',
                    font: {
                        size: 18,
                        weight: 'bold'
                    },
                    color: '#2D2D2D'
                },
                legend: {
                    display: true,
                    position: 'bottom',
                    labels: {
                        font: {
                            size: 14
                        },
                        color: '#2D2D2D'
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(45, 45, 45, 0.9)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: '#FF6B35',
                    borderWidth: 1,
                    callbacks: {
                        label: function(context) {
                            return 'Market Size: R' + context.raw + ' billion';
                        }
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    ticks: {
                        color: '#2D2D2D',
                        font: {
                            size: 12
                        }
                    }
                },
                y: {
                    beginAtZero: false,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    ticks: {
                        color: '#2D2D2D',
                        font: {
                            size: 12
                        },
                        callback: function(value) {
                            return 'R' + value + 'B';
                        }
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    });

    // Tab functionality
    const tabs = document.querySelectorAll('.tab');
    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Remove active class from all tabs and contents
            document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
            
            // Add active class to clicked tab and corresponding content
            this.classList.add('active');
            const tabId = this.getAttribute('data-tab');
            document.getElementById(tabId).classList.add('active');
        });
    });

    // Animate metric cards on scroll
    const metricCards = document.querySelectorAll('.metric-card');
    const driverCards = document.querySelectorAll('.driver-card');
    const segmentCards = document.querySelectorAll('.segment-card');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.transform = 'translateY(0)';
                entry.target.style.opacity = '1';
            }
        });
    }, { 
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });

    // Initialize animation states and observe elements
    [...metricCards, ...driverCards, ...segmentCards].forEach((card, index) => {
        card.style.transform = 'translateY(30px)';
        card.style.opacity = '0';
        card.style.transition = `all 0.6s ease-out ${index * 0.1}s`;
        observer.observe(card);
    });

    // Counter animation for metric values
    function animateCounter(element, target, duration = 2000) {
        const start = 0;
        const increment = target / (duration / 16);
        let current = start;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            
            // Format the number based on the target
            let displayValue;
            if (target >= 1000000) {
                displayValue = (current / 1000000).toFixed(1) + 'M';
            } else if (target >= 1000) {
                displayValue = (current / 1000).toFixed(1) + 'K';
            } else if (target < 1) {
                displayValue = current.toFixed(0) + '%';
            } else {
                displayValue = current.toFixed(0);
            }
            
            element.textContent = displayValue;
        }, 16);
    }

    // Trigger counter animations when metric cards come into view
    const metricObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const valueElement = entry.target.querySelector('.metric-value');
                const originalText = valueElement.textContent;
                
                // Extract numeric value
                let numericValue;
                if (originalText.includes('R') && originalText.includes('B')) {
                    numericValue = parseFloat(originalText.replace(/[^\d.]/g, ''));
                } else if (originalText.includes('%')) {
                    numericValue = parseFloat(originalText.replace('%', ''));
                } else if (originalText.includes('M')) {
                    numericValue = parseFloat(originalText.replace(/[^\d.]/g, '')) * 1000000;
                } else {
                    numericValue = parseFloat(originalText.replace(/[^\d.]/g, ''));
                }
                
                // Start animation
                setTimeout(() => {
                    if (originalText.includes('R') && originalText.includes('B')) {
                        animateCounter(valueElement, numericValue, 1500);
                        setTimeout(() => {
                            valueElement.textContent = originalText;
                        }, 1500);
                    } else if (originalText.includes('%')) {
                        animateCounter(valueElement, numericValue, 1500);
                        setTimeout(() => {
                            valueElement.textContent = originalText;
                        }, 1500);
                    } else if (originalText.includes('M')) {
                        animateCounter(valueElement, numericValue, 1500);
                        setTimeout(() => {
                            valueElement.textContent = originalText;
                        }, 1500);
                    }
                }, 300);
                
                metricObserver.unobserve(entry.target);
            }
        });
    }, { threshold: 0.5 });

    metricCards.forEach(card => {
        metricObserver.observe(card);
    });

    // Add hover effects to competitive matrix cells
    const matrixCells = document.querySelectorAll('.competitive-matrix td.strength, .competitive-matrix td.weakness, .competitive-matrix td.neutral');
    matrixCells.forEach(cell => {
        cell.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05)';
            this.style.transition = 'transform 0.2s ease';
        });
        
        cell.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });

    // Smooth scrolling for any internal links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});
