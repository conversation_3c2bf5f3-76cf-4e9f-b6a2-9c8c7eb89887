document.addEventListener('DOMContentLoaded', function() {
    // Initialize revenue chart
    const ctx = document.getElementById('revenueChart').getContext('2d');
    const revenueChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['Year 1', 'Year 2', 'Year 3'],
            datasets: [
                {
                    label: 'Lumerous Revenue',
                    data: [3600000, 7200000, 14400000],
                    backgroundColor: '#FF6B35',
                    borderColor: '#E05A2B',
                    borderWidth: 2,
                    borderRadius: 8,
                    borderSkipped: false,
                },
                {
                    label: 'Additional Services',
                    data: [1200000, 2400000, 4800000],
                    backgroundColor: '#2ecc71',
                    borderColor: '#27ae60',
                    borderWidth: 2,
                    borderRadius: 8,
                    borderSkipped: false,
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'Revenue Growth Projection',
                    font: {
                        size: 18,
                        weight: 'bold'
                    },
                    color: '#2D2D2D'
                },
                legend: {
                    display: true,
                    position: 'bottom',
                    labels: {
                        font: {
                            size: 14
                        },
                        color: '#2D2D2D',
                        usePointStyle: true,
                        padding: 20
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(45, 45, 45, 0.9)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: '#FF6B35',
                    borderWidth: 1,
                    callbacks: {
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            label += 'R' + (context.raw / 1000000).toFixed(1) + 'M';
                            return label;
                        }
                    }
                }
            },
            scales: {
                x: {
                    stacked: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    ticks: {
                        color: '#2D2D2D',
                        font: {
                            size: 12
                        }
                    }
                },
                y: {
                    stacked: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    ticks: {
                        color: '#2D2D2D',
                        font: {
                            size: 12
                        },
                        callback: function(value) {
                            return 'R' + (value / 1000000) + 'M';
                        }
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    });

    // Update range value displays
    document.getElementById('premiumRate').addEventListener('input', function() {
        document.getElementById('premiumRateValue').textContent = this.value + '%';
        calculateProjection();
    });
    
    document.getElementById('churnRate').addEventListener('input', function() {
        document.getElementById('churnRateValue').textContent = this.value + '%';
        calculateProjection();
    });

    // Calculate projection when inputs change
    document.getElementById('newUsers').addEventListener('input', calculateProjection);
    document.getElementById('newSchools').addEventListener('input', calculateProjection);

    // Initial calculation
    calculateProjection();

    function calculateProjection() {
        const newUsers = parseInt(document.getElementById('newUsers').value) || 0;
        const premiumRate = parseInt(document.getElementById('premiumRate').value) || 0;
        const newSchools = parseInt(document.getElementById('newSchools').value) || 0;
        const churnRate = parseInt(document.getElementById('churnRate').value) || 0;
        
        // Simplified calculation for demo purposes
        const baseRevenue = newUsers * 200; // R200 per user
        const premiumRevenue = baseRevenue * (premiumRate / 100) * 0.5; // 50% premium uplift
        const schoolRevenue = newSchools * 5000; // R5000 per school
        const grossRevenue = baseRevenue + premiumRevenue + schoolRevenue;
        const churnAdjustedRevenue = grossRevenue * (1 - (churnRate / 100));
        
        document.getElementById('projectedRevenue').textContent = 'R' + Math.round(churnAdjustedRevenue).toLocaleString();
        
        // Determine breakeven point
        const breakevenUsers = 1500;
        const monthsToBreakeven = Math.ceil(breakevenUsers / newUsers);
        document.getElementById('breakevenPoint').textContent = monthsToBreakeven <= 12 ? 
            'Month ' + monthsToBreakeven : 
            'Beyond 12 months at current rate';
    }

    // Animate revenue streams on scroll
    const revenueStreams = document.querySelectorAll('.revenue-stream');
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, { 
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });

    revenueStreams.forEach((stream, index) => {
        stream.style.opacity = '0';
        stream.style.transform = 'translateY(30px)';
        stream.style.transition = `all 0.6s ease-out ${index * 0.1}s`;
        observer.observe(stream);
    });

    // Animate allocation items
    const allocationItems = document.querySelectorAll('.allocation-item');
    const allocationObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateX(0)';
            }
        });
    }, { threshold: 0.1 });

    allocationItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateX(-30px)';
        item.style.transition = `all 0.6s ease-out ${index * 0.15}s`;
        allocationObserver.observe(item);
    });

    // Animate milestones
    const milestones = document.querySelectorAll('.milestone');
    const milestoneObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateX(0)';
            }
        });
    }, { threshold: 0.1 });

    milestones.forEach((milestone, index) => {
        milestone.style.opacity = '0';
        milestone.style.transform = 'translateX(-50px)';
        milestone.style.transition = `all 0.6s ease-out ${index * 0.2}s`;
        milestoneObserver.observe(milestone);
    });

    // Add counter animation for financial table
    function animateTableNumbers() {
        const tableRows = document.querySelectorAll('table tbody tr');
        tableRows.forEach((row, rowIndex) => {
            const cells = row.querySelectorAll('td');
            cells.forEach((cell, cellIndex) => {
                if (cellIndex > 0 && cell.textContent.includes('R')) {
                    const originalText = cell.textContent;
                    const numericValue = parseInt(originalText.replace(/[^\d]/g, ''));
                    
                    if (numericValue > 0) {
                        cell.textContent = 'R0';
                        
                        setTimeout(() => {
                            animateCounter(cell, numericValue, originalText, 1500);
                        }, rowIndex * 200 + cellIndex * 100);
                    }
                }
            });
        });
    }

    function animateCounter(element, target, originalFormat, duration = 1500) {
        const start = 0;
        const increment = target / (duration / 16);
        let current = start;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
                element.textContent = originalFormat;
            } else {
                // Format based on original text
                if (originalFormat.includes('%')) {
                    element.textContent = Math.round(current) + '%';
                } else if (originalFormat.includes(',')) {
                    element.textContent = 'R' + Math.round(current).toLocaleString();
                } else {
                    element.textContent = 'R' + Math.round(current);
                }
            }
        }, 16);
    }

    // Trigger table animation when it comes into view
    const financialTable = document.querySelector('.financial-table');
    const tableObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                setTimeout(animateTableNumbers, 500);
                tableObserver.unobserve(entry.target);
            }
        });
    }, { threshold: 0.3 });

    if (financialTable) {
        tableObserver.observe(financialTable);
    }

    // Add hover effects to calculator inputs
    const inputs = document.querySelectorAll('input');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.style.borderColor = '#FF6B35';
            this.style.boxShadow = '0 0 0 3px rgba(255, 107, 53, 0.1)';
        });
        
        input.addEventListener('blur', function() {
            this.style.borderColor = '#E9ECEF';
            this.style.boxShadow = 'none';
        });
    });

    // Add pulse animation to results
    function pulseResults() {
        const results = document.querySelector('.results');
        results.style.transform = 'scale(1.02)';
        setTimeout(() => {
            results.style.transform = 'scale(1)';
        }, 200);
    }

    // Trigger pulse when calculation changes
    const calculatorInputs = document.querySelectorAll('#newUsers, #newSchools, #premiumRate, #churnRate');
    calculatorInputs.forEach(input => {
        input.addEventListener('input', () => {
            setTimeout(pulseResults, 100);
        });
    });

    // Smooth scrolling for any internal links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});
